{"name": "@wechat-doodle/shared", "version": "1.0.0", "description": "Shared types, constants, and utilities", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "typecheck": "tsc --noEmit", "test": "jest", "lint": "eslint src --ext .ts"}, "dependencies": {}, "devDependencies": {"typescript": "^5.3.3", "@types/jest": "^29.5.11", "jest": "^29.7.0", "ts-jest": "^29.1.1"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "testMatch": ["**/__tests__/**/*.test.ts"], "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts"]}}