// Utility Functions

import { ValidationError, ValidationResult } from '../types';
import { VALIDATION_RULES, ERROR_CODES } from '../constants';

// Validation Utilities
export const validateUsername = (username: string): ValidationResult => {
  const errors: ValidationError[] = [];
  
  if (!username || username.trim().length === 0) {
    errors.push({
      field: 'username',
      message: '用户名不能为空',
      code: ERROR_CODES.VALIDATION_REQUIRED_FIELD
    });
  } else {
    if (username.length < VALIDATION_RULES.USERNAME.MIN_LENGTH) {
      errors.push({
        field: 'username',
        message: `用户名至少需要${VALIDATION_RULES.USERNAME.MIN_LENGTH}个字符`,
        code: ERROR_CODES.VALIDATION_OUT_OF_RANGE
      });
    }
    
    if (username.length > VALIDATION_RULES.USERNAME.MAX_LENGTH) {
      errors.push({
        field: 'username',
        message: `用户名不能超过${VALIDATION_RULES.USERNAME.MAX_LENGTH}个字符`,
        code: ERROR_CODES.VALIDATION_OUT_OF_RANGE
      });
    }
    
    if (!VALIDATION_RULES.USERNAME.PATTERN.test(username)) {
      errors.push({
        field: 'username',
        message: '用户名只能包含字母、数字、中文、下划线和连字符',
        code: ERROR_CODES.VALIDATION_INVALID_FORMAT
      });
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

export const validateScore = (score: number): ValidationResult => {
  const errors: ValidationError[] = [];
  
  if (typeof score !== 'number' || isNaN(score)) {
    errors.push({
      field: 'score',
      message: '分数必须是有效数字',
      code: ERROR_CODES.VALIDATION_INVALID_FORMAT
    });
  } else {
    if (score < VALIDATION_RULES.SCORE.MIN) {
      errors.push({
        field: 'score',
        message: '分数不能为负数',
        code: ERROR_CODES.VALIDATION_OUT_OF_RANGE
      });
    }
    
    if (score > VALIDATION_RULES.SCORE.MAX) {
      errors.push({
        field: 'score',
        message: '分数超出有效范围',
        code: ERROR_CODES.VALIDATION_OUT_OF_RANGE
      });
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// Date and Time Utilities
export const formatDate = (date: Date | string, format: 'short' | 'long' | 'time' = 'short'): string => {
  const d = typeof date === 'string' ? new Date(date) : date;
  
  if (isNaN(d.getTime())) {
    return '无效日期';
  }
  
  const options: Intl.DateTimeFormatOptions = {
    timeZone: 'Asia/Shanghai'
  };
  
  switch (format) {
    case 'short':
      options.year = 'numeric';
      options.month = '2-digit';
      options.day = '2-digit';
      break;
    case 'long':
      options.year = 'numeric';
      options.month = 'long';
      options.day = 'numeric';
      options.weekday = 'long';
      break;
    case 'time':
      options.hour = '2-digit';
      options.minute = '2-digit';
      break;
  }
  
  return new Intl.DateTimeFormat('zh-CN', options).format(d);
};

export const getRelativeTime = (date: Date | string): string => {
  const d = typeof date === 'string' ? new Date(date) : date;
  const now = new Date();
  const diffMs = now.getTime() - d.getTime();
  const diffSeconds = Math.floor(diffMs / 1000);
  const diffMinutes = Math.floor(diffSeconds / 60);
  const diffHours = Math.floor(diffMinutes / 60);
  const diffDays = Math.floor(diffHours / 24);
  
  if (diffSeconds < 60) {
    return '刚刚';
  } else if (diffMinutes < 60) {
    return `${diffMinutes}分钟前`;
  } else if (diffHours < 24) {
    return `${diffHours}小时前`;
  } else if (diffDays < 30) {
    return `${diffDays}天前`;
  } else {
    return formatDate(d, 'short');
  }
};

export const isToday = (date: Date | string): boolean => {
  const d = typeof date === 'string' ? new Date(date) : date;
  const today = new Date();
  
  return d.getFullYear() === today.getFullYear() &&
         d.getMonth() === today.getMonth() &&
         d.getDate() === today.getDate();
};

export const getDaysDifference = (date1: Date | string, date2: Date | string): number => {
  const d1 = typeof date1 === 'string' ? new Date(date1) : date1;
  const d2 = typeof date2 === 'string' ? new Date(date2) : date2;
  
  const diffTime = Math.abs(d2.getTime() - d1.getTime());
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};

// Number Formatting Utilities
export const formatScore = (score: number): string => {
  if (score >= 1000000) {
    return `${(score / 1000000).toFixed(1)}M`;
  } else if (score >= 1000) {
    return `${(score / 1000).toFixed(1)}K`;
  } else {
    return score.toString();
  }
};

export const formatCurrency = (amount: number): string => {
  return `¥${(amount / 100).toFixed(2)}`;
};

export const formatDuration = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  } else {
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  }
};

// String Utilities
export const truncateText = (text: string, maxLength: number, ellipsis: string = '...'): string => {
  if (text.length <= maxLength) {
    return text;
  }
  return text.substring(0, maxLength - ellipsis.length) + ellipsis;
};

export const sanitizeString = (str: string): string => {
  return str.replace(/[<>\"'&]/g, '');
};

export const generateRandomString = (length: number): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

// Array Utilities
export const shuffleArray = <T>(array: T[]): T[] => {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
};

export const groupBy = <T, K extends keyof T>(array: T[], key: K): Record<string, T[]> => {
  return array.reduce((groups, item) => {
    const group = String(item[key]);
    if (!groups[group]) {
      groups[group] = [];
    }
    groups[group].push(item);
    return groups;
  }, {} as Record<string, T[]>);
};

// Object Utilities
export const deepClone = <T>(obj: T): T => {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as unknown as T;
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as unknown as T;
  }
  
  if (typeof obj === 'object') {
    const clonedObj = {} as T;
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key]);
      }
    }
    return clonedObj;
  }
  
  return obj;
};

export const omit = <T extends Record<string, any>, K extends keyof T>(
  obj: T,
  keys: K[]
): Omit<T, K> => {
  const result = { ...obj };
  keys.forEach(key => delete result[key]);
  return result;
};

export const pick = <T extends Record<string, any>, K extends keyof T>(
  obj: T,
  keys: K[]
): Pick<T, K> => {
  const result = {} as Pick<T, K>;
  keys.forEach(key => {
    if (key in obj) {
      result[key] = obj[key];
    }
  });
  return result;
};

// Game Utilities
export const calculateLevel = (experience: number): number => {
  // Simple level calculation: level = floor(sqrt(experience / 100))
  return Math.floor(Math.sqrt(experience / 100)) + 1;
};

export const getExperienceForLevel = (level: number): number => {
  // Experience needed for a specific level
  return Math.pow(level - 1, 2) * 100;
};

export const calculateScoreMultiplier = (height: number, streak: number, powerUps: number): number => {
  const heightMultiplier = 1 + (height * 0.001);
  const streakMultiplier = 1 + (streak * 0.1);
  const powerUpMultiplier = 1 + (powerUps * 0.05);
  
  return heightMultiplier * streakMultiplier * powerUpMultiplier;
};

export const isHighScore = (currentScore: number, previousHighScore: number): boolean => {
  return currentScore > previousHighScore;
};

// WeChat Utilities
export const generateWeChatSignature = (params: Record<string, any>, key: string): string => {
  const sortedKeys = Object.keys(params).sort();
  const queryString = sortedKeys
    .filter(k => params[k] !== undefined && params[k] !== '')
    .map(k => `${k}=${params[k]}`)
    .join('&');
  
  // In production, use proper crypto library
  // This is a simplified version for demonstration
  return btoa(queryString + key).substring(0, 32);
};

export const parseWeChatShareQuery = (query: string): Record<string, string> => {
  const params: Record<string, string> = {};
  const pairs = query.split('&');
  
  for (const pair of pairs) {
    const [key, value] = pair.split('=');
    if (key && value) {
      params[decodeURIComponent(key)] = decodeURIComponent(value);
    }
  }
  
  return params;
};

// Cache Utilities
export const createCacheKey = (...parts: (string | number)[]): string => {
  return parts.join(':');
};

export const parseCacheKey = (key: string): string[] => {
  return key.split(':');
};

// Error Handling Utilities
export const createApiError = (
  code: string,
  message: string,
  details?: Record<string, any>
): { error: { code: string; message: string; details?: any; timestamp: string; requestId: string } } => {
  return {
    error: {
      code,
      message,
      details,
      timestamp: new Date().toISOString(),
      requestId: generateRandomString(12)
    }
  };
};

export const isApiError = (response: any): boolean => {
  return response && response.error && typeof response.error.code === 'string';
};

// Export all utilities
export default {
  // Validation
  validateUsername,
  validateScore,
  
  // Date and Time
  formatDate,
  getRelativeTime,
  isToday,
  getDaysDifference,
  
  // Number Formatting
  formatScore,
  formatCurrency,
  formatDuration,
  
  // String
  truncateText,
  sanitizeString,
  generateRandomString,
  
  // Array
  shuffleArray,
  groupBy,
  
  // Object
  deepClone,
  omit,
  pick,
  
  // Game
  calculateLevel,
  getExperienceForLevel,
  calculateScoreMultiplier,
  isHighScore,
  
  // WeChat
  generateWeChatSignature,
  parseWeChatShareQuery,
  
  // Cache
  createCacheKey,
  parseCacheKey,
  
  // Error Handling
  createApiError,
  isApiError
};