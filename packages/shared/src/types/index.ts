// User Types
export interface User {
  id: string;
  wechatId: string;
  username: string;
  avatar: string;
  nickname: string;
  friends: string[]; // User IDs
  totalScore: number;
  highScore: number;
  level: number;
  coins: number;
  achievements: string[]; // Achievement IDs
  lastLogin: Date;
  createdAt: Date;
  updatedAt: Date;
}

// Game Session Types
export interface GameSession {
  id: string;
  userId: string;
  score: number;
  duration: number; // in seconds
  platforms: number;
  powerUpsUsed: PowerUpUsage[];
  isHighScore: boolean;
  createdAt: Date;
}

export interface PowerUpUsage {
  type: PowerUpType;
  timestamp: number; // game timestamp
  effect: string;
}

export enum PowerUpType {
  DOUBLE_JUMP = 'double_jump',
  SHIELD = 'shield',
  MAGNET = 'magnet',
  BOOST = 'boost'
}

// Achievement Types
export interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  type: AchievementType;
  criteria: AchievementCriteria;
  reward: AchievementReward;
  rarity: AchievementRarity;
  createdAt: Date;
}

export enum AchievementType {
  SCORE = 'score',
  CONSISTENCY = 'consistency',
  SOCIAL = 'social',
  PROGRESSION = 'progression'
}

export interface AchievementCriteria {
  metric: string;
  threshold: number;
  timeframe?: string; // 'daily', 'weekly', 'monthly', 'all_time'
}

export interface AchievementReward {
  coins: number;
  items?: string[];
  title?: string;
}

export enum AchievementRarity {
  COMMON = 'common',
  RARE = 'rare',
  EPIC = 'epic',
  LEGENDARY = 'legendary'
}

// Friendship Types
export interface Friendship {
  id: string;
  userId: string;
  friendId: string;
  status: FriendshipStatus;
  createdAt: Date;
  lastInteraction?: Date;
}

export enum FriendshipStatus {
  PENDING = 'pending',
  ACCEPTED = 'accepted',
  BLOCKED = 'blocked'
}

// Challenge Types
export interface Challenge {
  id: string;
  challengerId: string;
  challengedId: string;
  type: ChallengeType;
  targetScore: number;
  status: ChallengeStatus;
  expiresAt: Date;
  completedAt?: Date;
  reward?: ChallengeReward;
  createdAt: Date;
}

export enum ChallengeType {
  HIGH_SCORE = 'high_score',
  DAILY = 'daily',
  STREAK = 'streak'
}

export enum ChallengeStatus {
  PENDING = 'pending',
  ACCEPTED = 'accepted',
  COMPLETED = 'completed',
  EXPIRED = 'expired',
  DECLINED = 'declined'
}

export interface ChallengeReward {
  coins: number;
  items?: string[];
  achievement?: string;
}

// Purchase Types
export interface Purchase {
  id: string;
  userId: string;
  itemType: PurchaseItemType;
  itemId: string;
  amount: number; // in yuan
  currency: 'CNY';
  paymentMethod: 'wechat_pay';
  status: PurchaseStatus;
  transactionId?: string;
  createdAt: Date;
  completedAt?: Date;
}

export enum PurchaseItemType {
  COINS = 'coins',
  SKIN = 'skin',
  POWER_UP = 'power_up',
  VIP = 'vip'
}

export enum PurchaseStatus {
  PENDING = 'pending',
  COMPLETED = 'completed',
  FAILED = 'failed',
  REFUNDED = 'refunded'
}

// Leaderboard Types
export interface LeaderboardEntry {
  userId: string;
  username: string;
  avatar: string;
  score: number;
  rank: number;
  isCurrentUser?: boolean;
  isFriend?: boolean;
}

export interface Leaderboard {
  type: LeaderboardType;
  timeframe: LeaderboardTimeframe;
  entries: LeaderboardEntry[];
  lastUpdated: Date;
}

export enum LeaderboardType {
  GLOBAL = 'global',
  FRIENDS = 'friends',
  REGIONAL = 'regional'
}

export enum LeaderboardTimeframe {
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
  ALL_TIME = 'all_time'
}

// Daily Rewards Types
export interface DailyReward {
  day: number; // 1-7
  type: RewardType;
  amount: number;
  itemId?: string;
  claimed: boolean;
  claimedAt?: Date;
}

export enum RewardType {
  COINS = 'coins',
  POWER_UP = 'power_up',
  SKIN = 'skin',
  ACHIEVEMENT = 'achievement'
}

// Game Configuration Types
export interface GameConfig {
  version: string;
  physics: PhysicsConfig;
  platforms: PlatformConfig;
  powerUps: PowerUpConfig[];
  achievements: Achievement[];
  dailyRewards: DailyReward[];
}

export interface PhysicsConfig {
  gravity: number;
  jumpVelocity: number;
  horizontalSpeed: number;
  bounceForce: number;
}

export interface PlatformConfig {
  baseWidth: number;
  minGap: number;
  maxGap: number;
  specialPlatformChance: number;
}

export interface PowerUpConfig {
  type: PowerUpType;
  duration: number;
  effect: Record<string, any>;
  rarity: number; // 0-1, probability of spawn
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: ApiError;
  message?: string;
}

export interface ApiError {
  code: string;
  message: string;
  details?: Record<string, any>;
  timestamp: string;
  requestId: string;
}

// WeChat Integration Types
export interface WeChatLoginData {
  code: string;
  userInfo?: WeChatUserInfo;
}

export interface WeChatUserInfo {
  nickName: string;
  avatarUrl: string;
  gender: number;
  city: string;
  province: string;
  country: string;
  language: string;
}

export interface WeChatShareData {
  title: string;
  desc: string;
  link: string;
  imgUrl: string;
  type?: string;
  dataUrl?: string;
}

// Socket Event Types
export interface SocketEvents {
  // Client to Server
  'user:join': (data: { userId: string; gameId?: string }) => void;
  'user:leave': () => void;
  'game:start': (data: { userId: string }) => void;
  'game:end': (data: { userId: string; score: number; duration: number }) => void;
  'challenge:send': (data: { challengerId: string; challengedId: string; targetScore: number }) => void;
  'challenge:accept': (data: { challengeId: string }) => void;
  'challenge:decline': (data: { challengeId: string }) => void;

  // Server to Client
  'leaderboard:update': (data: Leaderboard) => void;
  'challenge:received': (data: Challenge) => void;
  'challenge:completed': (data: Challenge) => void;
  'friend:online': (data: { userId: string; status: 'online' | 'offline' }) => void;
  'notification': (data: { type: string; message: string; data?: any }) => void;
}

// Game State Types
export interface GameState {
  isPlaying: boolean;
  isPaused: boolean;
  score: number;
  platforms: Platform[];
  player: Player;
  powerUps: ActivePowerUp[];
  camera: Camera;
  gameTime: number;
}

export interface Platform {
  id: string;
  x: number;
  y: number;
  width: number;
  type: PlatformType;
  isMoving?: boolean;
  movementSpeed?: number;
  movementRange?: number;
}

export enum PlatformType {
  NORMAL = 'normal',
  BREAKING = 'breaking',
  MOVING = 'moving',
  SPRING = 'spring',
  DISAPPEARING = 'disappearing'
}

export interface Player {
  x: number;
  y: number;
  velocityX: number;
  velocityY: number;
  isJumping: boolean;
  direction: 'left' | 'right';
  skin: string;
}

export interface ActivePowerUp {
  type: PowerUpType;
  startTime: number;
  duration: number;
  isActive: boolean;
}

export interface Camera {
  x: number;
  y: number;
  zoom: number;
}

// Form Validation Types
export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

// Utility Types
export type Omit<T, K extends keyof T> = Pick<T, Exclude<keyof T, K>>;
export type Partial<T> = {
  [P in keyof T]?: T[P];
};
export type Required<T> = {
  [P in keyof T]-?: T[P];
};

// Export all types as namespace
export * from './api';
export * from './game';
export * from './social';
export * from './wechat';