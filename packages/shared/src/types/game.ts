// Game-specific Types

export interface GameSettings {
  soundEnabled: boolean;
  musicEnabled: boolean;
  vibrationEnabled: boolean;
  controlScheme: 'tilt' | 'touch';
  difficulty: 'easy' | 'normal' | 'hard';
  graphics: 'low' | 'medium' | 'high';
}

export interface GameStats {
  totalJumps: number;
  totalDistance: number;
  totalPlatforms: number;
  powerUpsCollected: number;
  specialPlatformsHit: number;
  perfectLandings: number;
  consecutiveJumps: number;
  maxHeight: number;
}

export interface GameAssets {
  sprites: {
    player: string[];
    platforms: string[];
    powerUps: string[];
    background: string[];
    effects: string[];
  };
  sounds: {
    jump: string;
    land: string;
    powerUp: string;
    achievement: string;
    gameOver: string;
    background: string;
  };
  music: {
    gameplay: string;
    menu: string;
  };
}

export interface PlatformData {
  id: string;
  x: number;
  y: number;
  width: number;
  height: number;
  type: PlatformType;
  texture: string;
  isCollectable?: boolean;
  powerUp?: PowerUpType;
  moving?: {
    speed: number;
    range: number;
    direction: 'horizontal' | 'vertical';
  };
  breaking?: {
    delay: number;
    particles: boolean;
  };
}

export enum PlatformType {
  NORMAL = 'normal',
  SPRING = 'spring',
  MOVING = 'moving',
  BREAKING = 'breaking',
  DISAPPEARING = 'disappearing',
  ICE = 'ice',
  CLOUD = 'cloud'
}

export interface PowerUpData {
  id: string;
  type: PowerUpType;
  x: number;
  y: number;
  duration: number;
  effect: PowerUpEffect;
  rarity: number;
  icon: string;
}

export enum PowerUpType {
  DOUBLE_JUMP = 'double_jump',
  SHIELD = 'shield',
  MAGNET = 'magnet',
  BOOST = 'boost',
  SLOW_MOTION = 'slow_motion',
  COIN_MULTIPLIER = 'coin_multiplier'
}

export interface PowerUpEffect {
  jumpMultiplier?: number;
  speedMultiplier?: number;
  protection?: boolean;
  magnetRadius?: number;
  coinMultiplier?: number;
  timeMultiplier?: number;
}

export interface PlayerData {
  id: string;
  position: {
    x: number;
    y: number;
  };
  velocity: {
    x: number;
    y: number;
  };
  state: PlayerState;
  skin: PlayerSkin;
  animations: {
    current: string;
    frame: number;
    speed: number;
  };
  effects: ActiveEffect[];
}

export enum PlayerState {
  IDLE = 'idle',
  JUMPING = 'jumping',
  FALLING = 'falling',
  LANDING = 'landing',
  DEAD = 'dead'
}

export interface PlayerSkin {
  id: string;
  name: string;
  texture: string;
  animations: Record<string, string[]>;
  unlocked: boolean;
  price?: number;
}

export interface ActiveEffect {
  type: PowerUpType;
  startTime: number;
  duration: number;
  intensity: number;
}

export interface GamePhysics {
  gravity: number;
  jumpForce: number;
  horizontalSpeed: number;
  maxFallSpeed: number;
  friction: number;
  bounce: number;
  platformCollision: {
    width: number;
    height: number;
    offset: { x: number; y: number };
  };
}

export interface CameraData {
  x: number;
  y: number;
  targetY: number;
  smoothing: number;
  bounds: {
    left: number;
    right: number;
    top: number;
    bottom: number;
  };
  shake: {
    intensity: number;
    duration: number;
  };
}

export interface ParticleSystem {
  id: string;
  type: 'explosion' | 'trail' | 'stars' | 'coins';
  x: number;
  y: number;
  particles: Particle[];
  duration: number;
  autoDestroy: boolean;
}

export interface Particle {
  x: number;
  y: number;
  velocityX: number;
  velocityY: number;
  life: number;
  maxLife: number;
  size: number;
  color: string;
  alpha: number;
}

export interface GameLevel {
  id: string;
  name: string;
  minHeight: number;
  maxHeight: number;
  platformDensity: number;
  powerUpChance: number;
  specialPlatformChance: number;
  backgroundTheme: string;
  difficulty: number;
}

export interface ScoreMultiplier {
  base: number;
  height: number;
  streak: number;
  powerUp: number;
  difficulty: number;
}

export interface GameEvent {
  type: GameEventType;
  timestamp: number;
  data: any;
}

export enum GameEventType {
  GAME_START = 'game_start',
  GAME_END = 'game_end',
  PLAYER_JUMP = 'player_jump',
  PLAYER_LAND = 'player_land',
  PLATFORM_HIT = 'platform_hit',
  POWER_UP_COLLECTED = 'power_up_collected',
  ACHIEVEMENT_UNLOCKED = 'achievement_unlocked',
  HIGH_SCORE = 'high_score',
  MILESTONE_REACHED = 'milestone_reached'
}