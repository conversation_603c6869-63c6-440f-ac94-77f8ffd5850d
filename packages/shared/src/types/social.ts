// Social Features Types

export interface SocialProfile {
  userId: string;
  username: string;
  displayName: string;
  avatar: string;
  level: number;
  experience: number;
  title?: string;
  badges: Badge[];
  stats: SocialStats;
  isOnline: boolean;
  lastSeen?: Date;
  privacy: PrivacySettings;
}

export interface SocialStats {
  gamesPlayed: number;
  totalScore: number;
  highScore: number;
  averageScore: number;
  winStreak: number;
  maxWinStreak: number;
  challengesWon: number;
  challengesLost: number;
  friendsCount: number;
  achievementsUnlocked: number;
  rank: number;
  percentile: number;
}

export interface Badge {
  id: string;
  name: string;
  description: string;
  icon: string;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  unlockedAt: Date;
  category: BadgeCategory;
}

export enum BadgeCategory {
  ACHIEVEMENT = 'achievement',
  MILESTONE = 'milestone',
  SOCIAL = 'social',
  SEASONAL = 'seasonal',
  SPECIAL = 'special'
}

export interface FriendInvite {
  id: string;
  inviterId: string;
  inviterName: string;
  inviterAvatar: string;
  inviteeId: string;
  message?: string;
  status: InviteStatus;
  createdAt: Date;
  expiresAt: Date;
  respondedAt?: Date;
}

export enum InviteStatus {
  PENDING = 'pending',
  ACCEPTED = 'accepted',
  DECLINED = 'declined',
  EXPIRED = 'expired',
  CANCELLED = 'cancelled'
}

export interface SocialChallenge {
  id: string;
  title: string;
  description: string;
  type: SocialChallengeType;
  challengerId: string;
  challengedIds: string[];
  requirements: ChallengeRequirement;
  rewards: ChallengeReward;
  status: ChallengeStatus;
  participants: ChallengeParticipant[];
  startTime: Date;
  endTime: Date;
  createdAt: Date;
}

export enum SocialChallengeType {
  SCORE_BATTLE = 'score_battle',
  STREAK_CHALLENGE = 'streak_challenge',
  TIME_ATTACK = 'time_attack',
  SURVIVAL = 'survival',
  PRECISION = 'precision'
}

export interface ChallengeRequirement {
  targetScore?: number;
  targetStreak?: number;
  timeLimit?: number;
  attempts?: number;
  difficulty?: string;
  powerUpsAllowed?: boolean;
}

export interface ChallengeReward {
  coins?: number;
  experience?: number;
  powerUps?: string[];
  badges?: string[];
  title?: string;
  multiplier?: number;
}

export enum ChallengeStatus {
  PENDING = 'pending',
  ACTIVE = 'active',
  COMPLETED = 'completed',
  EXPIRED = 'expired',
  CANCELLED = 'cancelled'
}

export interface ChallengeParticipant {
  userId: string;
  username: string;
  avatar: string;
  score: number;
  attempts: number;
  bestScore: number;
  completedAt?: Date;
  status: ParticipantStatus;
}

export enum ParticipantStatus {
  JOINED = 'joined',
  COMPLETED = 'completed',
  FAILED = 'failed',
  QUIT = 'quit'
}

export interface SocialGift {
  id: string;
  senderId: string;
  recipientId: string;
  giftType: GiftType;
  itemId: string;
  itemName: string;
  itemIcon: string;
  message?: string;
  occasion?: string;
  status: GiftStatus;
  sentAt: Date;
  claimedAt?: Date;
  expiresAt: Date;
}

export enum GiftType {
  COINS = 'coins',
  POWER_UP = 'power_up',
  SKIN = 'skin',
  LIVES = 'lives',
  BOOST = 'boost'
}

export enum GiftStatus {
  SENT = 'sent',
  CLAIMED = 'claimed',
  EXPIRED = 'expired',
  RETURNED = 'returned'
}

export interface SocialNotification {
  id: string;
  userId: string;
  type: NotificationType;
  title: string;
  message: string;
  data?: any;
  isRead: boolean;
  priority: NotificationPriority;
  createdAt: Date;
  expiresAt?: Date;
}

export enum NotificationType {
  FRIEND_REQUEST = 'friend_request',
  CHALLENGE_RECEIVED = 'challenge_received',
  CHALLENGE_COMPLETED = 'challenge_completed',
  GIFT_RECEIVED = 'gift_received',
  ACHIEVEMENT_UNLOCKED = 'achievement_unlocked',
  LEADERBOARD_POSITION = 'leaderboard_position',
  DAILY_REWARD = 'daily_reward',
  SYSTEM_MESSAGE = 'system_message'
}

export enum NotificationPriority {
  LOW = 'low',
  NORMAL = 'normal',
  HIGH = 'high',
  URGENT = 'urgent'
}

export interface PrivacySettings {
  profileVisible: 'public' | 'friends' | 'private';
  scoreVisible: 'public' | 'friends' | 'private';
  onlineStatus: 'public' | 'friends' | 'hidden';
  allowFriendRequests: boolean;
  allowChallenges: boolean;
  allowGifts: boolean;
  showInLeaderboards: boolean;
}

export interface SocialFeed {
  id: string;
  userId: string;
  username: string;
  avatar: string;
  type: FeedItemType;
  content: FeedContent;
  timestamp: Date;
  likes: number;
  comments: FeedComment[];
  isLiked: boolean;
  visibility: 'public' | 'friends';
}

export enum FeedItemType {
  HIGH_SCORE = 'high_score',
  ACHIEVEMENT = 'achievement',
  MILESTONE = 'milestone',
  CHALLENGE_COMPLETED = 'challenge_completed',
  LEVEL_UP = 'level_up',
  STREAK = 'streak'
}

export interface FeedContent {
  title: string;
  description: string;
  image?: string;
  data?: any;
}

export interface FeedComment {
  id: string;
  userId: string;
  username: string;
  avatar: string;
  message: string;
  timestamp: Date;
  likes: number;
  isLiked: boolean;
}

export interface SocialGroup {
  id: string;
  name: string;
  description: string;
  avatar: string;
  ownerId: string;
  members: GroupMember[];
  maxMembers: number;
  isPublic: boolean;
  tags: string[];
  stats: GroupStats;
  createdAt: Date;
}

export interface GroupMember {
  userId: string;
  username: string;
  avatar: string;
  role: GroupRole;
  joinedAt: Date;
  lastActive: Date;
  contribution: number;
}

export enum GroupRole {
  OWNER = 'owner',
  ADMIN = 'admin',
  MODERATOR = 'moderator',
  MEMBER = 'member'
}

export interface GroupStats {
  totalScore: number;
  averageScore: number;
  gamesPlayed: number;
  challengesCompleted: number;
  rank: number;
  level: number;
}