// WeChat Integration Types

export interface WeChatConfig {
  appId: string;
  appSecret: string;
  mchId?: string; // Merchant ID for WeChat Pay
  apiKey?: string; // API Key for WeChat Pay
  debug: boolean;
  jsApiList: string[];
}

export interface WeChatAuthResponse {
  access_token: string;
  expires_in: number;
  refresh_token: string;
  openid: string;
  scope: string;
  unionid?: string;
}

export interface WeChatUserProfile {
  openid: string;
  nickname: string;
  sex: number; // 1: male, 2: female, 0: unknown
  province: string;
  city: string;
  country: string;
  headimgurl: string;
  privilege: string[];
  unionid?: string;
  language?: string;
}

export interface WeChatMiniProgramAuth {
  code: string;
  encryptedData?: string;
  iv?: string;
  signature?: string;
  rawData?: string;
}

export interface WeChatSessionKey {
  openid: string;
  session_key: string;
  unionid?: string;
}

export interface WeChatDecryptedData {
  openId: string;
  nickName: string;
  gender: number;
  city: string;
  province: string;
  country: string;
  avatarUrl: string;
  unionId?: string;
  watermark: {
    timestamp: number;
    appid: string;
  };
}

// WeChat Pay Types
export interface WeChatPayOrder {
  orderId: string;
  userId: string;
  productName: string;
  amount: number; // in cents (分)
  currency: 'CNY';
  notifyUrl: string;
  redirectUrl?: string;
  clientIP: string;
  timestamp: number;
  nonceStr: string;
}

export interface WeChatPayResponse {
  return_code: 'SUCCESS' | 'FAIL';
  return_msg: string;
  appid?: string;
  mch_id?: string;
  nonce_str?: string;
  sign?: string;
  result_code?: 'SUCCESS' | 'FAIL';
  prepay_id?: string;
  trade_type?: string;
  code_url?: string;
  err_code?: string;
  err_code_des?: string;
}

export interface WeChatPayNotification {
  return_code: 'SUCCESS' | 'FAIL';
  return_msg: string;
  appid: string;
  mch_id: string;
  device_info?: string;
  nonce_str: string;
  sign: string;
  sign_type?: string;
  result_code: 'SUCCESS' | 'FAIL';
  err_code?: string;
  err_code_des?: string;
  openid: string;
  is_subscribe: 'Y' | 'N';
  trade_type: string;
  bank_type: string;
  total_fee: number;
  settlement_total_fee?: number;
  fee_type?: string;
  cash_fee: number;
  cash_fee_type?: string;
  transaction_id: string;
  out_trade_no: string;
  attach?: string;
  time_end: string;
}

// WeChat Sharing Types
export interface WeChatShareConfig {
  title: string;
  desc: string;
  link: string;
  imgUrl: string;
  type?: 'link' | 'music' | 'video';
  dataUrl?: string;
  success?: () => void;
  cancel?: () => void;
  fail?: (error: any) => void;
}

export interface WeChatShareToTimelineConfig {
  title: string;
  link: string;
  imgUrl: string;
  success?: () => void;
  cancel?: () => void;
  fail?: (error: any) => void;
}

export interface WeChatShareGameData {
  title: string;
  description: string;
  imageUrl: string;
  query?: string;
  shareTicket?: string;
}

// WeChat MiniProgram APIs
export interface WeChatStorageData {
  key: string;
  data: any;
  success?: () => void;
  fail?: (error: any) => void;
  complete?: () => void;
}

export interface WeChatNetworkRequest {
  url: string;
  data?: any;
  header?: Record<string, string>;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  dataType?: 'json' | 'text';
  responseType?: 'text' | 'arraybuffer';
  success?: (result: any) => void;
  fail?: (error: any) => void;
  complete?: () => void;
}

export interface WeChatUploadFileOptions {
  url: string;
  filePath: string;
  name: string;
  header?: Record<string, string>;
  formData?: Record<string, any>;
  success?: (result: any) => void;
  fail?: (error: any) => void;
  complete?: () => void;
}

// WeChat Analytics
export interface WeChatAnalyticsEvent {
  eventName: string;
  data?: Record<string, any>;
}

export interface WeChatUserBehavior {
  action: string;
  target: string;
  timestamp: number;
  data?: any;
}

// WeChat Canvas/Game APIs
export interface WeChatCanvasContext {
  canvas: any;
  width: number;
  height: number;
  createImage: () => any;
  createImageData: (width: number, height: number) => any;
  getImageData: (x: number, y: number, width: number, height: number) => any;
  putImageData: (imageData: any, x: number, y: number) => void;
}

export interface WeChatGameClubButton {
  id: string;
  text: string;
  style: {
    left: number;
    top: number;
    width: number;
    height: number;
    backgroundColor?: string;
    borderColor?: string;
    borderWidth?: number;
    borderRadius?: number;
    color?: string;
    fontSize?: number;
    textAlign?: 'left' | 'center' | 'right';
  };
  icon?: string;
  image?: string;
}

// WeChat Social APIs
export interface WeChatGroupInfo {
  groupId: string;
  groupName: string;
  memberCount: number;
  maxMemberCount: number;
}

export interface WeChatRankData {
  score: number;
  updateTime: number;
}

export interface WeChatCloudFunction {
  name: string;
  data?: any;
  success?: (result: any) => void;
  fail?: (error: any) => void;
  complete?: () => void;
}

// WeChat Settings
export interface WeChatSettings {
  authSetting: {
    'scope.userInfo'?: boolean;
    'scope.userLocation'?: boolean;
    'scope.address'?: boolean;
    'scope.invoiceTitle'?: boolean;
    'scope.werun'?: boolean;
    'scope.record'?: boolean;
    'scope.writePhotosAlbum'?: boolean;
    'scope.camera'?: boolean;
  };
}

// WeChat Error Types
export interface WeChatError {
  errCode: number;
  errMsg: string;
}

export enum WeChatErrorCode {
  SYSTEM_ERROR = -1,
  USER_CANCEL = 0,
  NETWORK_ERROR = 1,
  INVALID_PARAM = 2,
  AUTH_DENIED = 4,
  NO_NETWORK = 6
}