// API Request/Response Types

export interface LoginRequest {
  code: string;
  userInfo?: {
    nickName: string;
    avatarUrl: string;
  };
}

export interface LoginResponse {
  token: string;
  user: {
    id: string;
    username: string;
    avatar: string;
    coins: number;
    level: number;
  };
  expiresIn: string;
}

export interface GameSessionRequest {
  score: number;
  duration: number;
  platforms: number;
  powerUpsUsed: Array<{
    type: string;
    timestamp: number;
  }>;
}

export interface LeaderboardRequest {
  type: 'global' | 'friends' | 'regional';
  timeframe: 'daily' | 'weekly' | 'monthly' | 'all_time';
  limit?: number;
  offset?: number;
}

export interface ChallengeRequest {
  challengedId: string;
  type: 'high_score' | 'daily' | 'streak';
  targetScore: number;
  message?: string;
}

export interface PurchaseRequest {
  itemType: 'coins' | 'skin' | 'power_up' | 'vip';
  itemId: string;
  amount: number;
}

export interface UserStatsResponse {
  totalGames: number;
  totalScore: number;
  highScore: number;
  averageScore: number;
  totalPlayTime: number;
  achievements: number;
  rank: number;
  level: number;
  experience: number;
  coins: number;
}

export interface FriendsResponse {
  friends: Array<{
    id: string;
    username: string;
    avatar: string;
    isOnline: boolean;
    highScore: number;
    level: number;
    lastSeen?: string;
  }>;
  totalCount: number;
}

export interface AchievementsResponse {
  achievements: Array<{
    id: string;
    name: string;
    description: string;
    icon: string;
    unlockedAt?: string;
    progress?: number;
    maxProgress?: number;
  }>;
  totalUnlocked: number;
  totalAvailable: number;
}

export interface DailyRewardsResponse {
  currentStreak: number;
  maxStreak: number;
  rewards: Array<{
    day: number;
    type: string;
    amount: number;
    itemId?: string;
    claimed: boolean;
    canClaim: boolean;
  }>;
  nextResetTime: string;
}

// Pagination Types
export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}