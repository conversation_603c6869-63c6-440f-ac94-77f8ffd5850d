// Game Constants
export const GAME_CONFIG = {
  PHYSICS: {
    GRAVITY: 980, // pixels per second squared
    JUMP_VELOCITY: -450, // pixels per second
    HORIZONTAL_SPEED: 200, // pixels per second
    MAX_FALL_SPEED: 800, // pixels per second
    BOUNCE_DAMPING: 0.8,
    FRICTION: 0.95
  },
  
  PLATFORMS: {
    BASE_WIDTH: 80,
    MIN_GAP: 30,
    MAX_GAP: 120,
    SPECIAL_PLATFORM_CHANCE: 0.15,
    POWER_UP_CHANCE: 0.08
  },
  
  SCORING: {
    BASE_SCORE_PER_PLATFORM: 10,
    HEIGHT_MULTIPLIER: 0.1,
    STREAK_BONUS: 5,
    POWER_UP_BONUS: 25,
    PERFECT_LANDING_BONUS: 15
  },
  
  PLAYER: {
    SIZE: { width: 32, height: 32 },
    MAX_HORIZONTAL_SPEED: 300,
    ACCELERATION: 500
  },
  
  CAMERA: {
    SMOOTHING: 0.1,
    OFFSET_Y: 200
  }
} as const;

// API Endpoints
export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/api/auth/login',
    LOGOUT: '/api/auth/logout',
    REFRESH: '/api/auth/refresh',
    PROFILE: '/api/auth/profile'
  },
  
  GAME: {
    SESSION: '/api/game/session',
    LEADERBOARD: '/api/game/leaderboard',
    STATS: '/api/game/stats',
    CONFIG: '/api/game/config'
  },
  
  SOCIAL: {
    FRIENDS: '/api/social/friends',
    CHALLENGES: '/api/social/challenges',
    GIFTS: '/api/social/gifts',
    NOTIFICATIONS: '/api/social/notifications'
  },
  
  USER: {
    PROFILE: '/api/user/profile',
    ACHIEVEMENTS: '/api/user/achievements',
    DAILY_REWARDS: '/api/user/daily-rewards',
    PURCHASES: '/api/user/purchases'
  },
  
  WECHAT: {
    AUTH: '/api/wechat/auth',
    PAY: '/api/wechat/pay',
    SHARE: '/api/wechat/share'
  }
} as const;

// Error Codes
export const ERROR_CODES = {
  // Authentication Errors
  AUTH_TOKEN_EXPIRED: 'AUTH_TOKEN_EXPIRED',
  AUTH_TOKEN_INVALID: 'AUTH_TOKEN_INVALID',
  AUTH_USER_NOT_FOUND: 'AUTH_USER_NOT_FOUND',
  AUTH_WECHAT_CODE_INVALID: 'AUTH_WECHAT_CODE_INVALID',
  
  // Game Errors
  GAME_SESSION_INVALID: 'GAME_SESSION_INVALID',
  GAME_SCORE_INVALID: 'GAME_SCORE_INVALID',
  GAME_CONFIG_ERROR: 'GAME_CONFIG_ERROR',
  
  // Social Errors
  FRIEND_REQUEST_EXISTS: 'FRIEND_REQUEST_EXISTS',
  FRIEND_NOT_FOUND: 'FRIEND_NOT_FOUND',
  CHALLENGE_EXPIRED: 'CHALLENGE_EXPIRED',
  CHALLENGE_ALREADY_ACCEPTED: 'CHALLENGE_ALREADY_ACCEPTED',
  
  // Purchase Errors
  PURCHASE_INSUFFICIENT_FUNDS: 'PURCHASE_INSUFFICIENT_FUNDS',
  PURCHASE_ITEM_NOT_FOUND: 'PURCHASE_ITEM_NOT_FOUND',
  PURCHASE_ALREADY_OWNED: 'PURCHASE_ALREADY_OWNED',
  
  // WeChat Errors
  WECHAT_API_ERROR: 'WECHAT_API_ERROR',
  WECHAT_PAY_ERROR: 'WECHAT_PAY_ERROR',
  WECHAT_SHARE_ERROR: 'WECHAT_SHARE_ERROR',
  
  // Validation Errors
  VALIDATION_REQUIRED_FIELD: 'VALIDATION_REQUIRED_FIELD',
  VALIDATION_INVALID_FORMAT: 'VALIDATION_INVALID_FORMAT',
  VALIDATION_OUT_OF_RANGE: 'VALIDATION_OUT_OF_RANGE',
  
  // System Errors
  DATABASE_ERROR: 'DATABASE_ERROR',
  CACHE_ERROR: 'CACHE_ERROR',
  NETWORK_ERROR: 'NETWORK_ERROR',
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  MAINTENANCE_MODE: 'MAINTENANCE_MODE'
} as const;

// Success Messages
export const SUCCESS_MESSAGES = {
  USER_CREATED: 'User created successfully',
  LOGIN_SUCCESS: 'Login successful',
  LOGOUT_SUCCESS: 'Logout successful',
  PROFILE_UPDATED: 'Profile updated successfully',
  GAME_SESSION_SAVED: 'Game session saved successfully',
  FRIEND_REQUEST_SENT: 'Friend request sent successfully',
  CHALLENGE_SENT: 'Challenge sent successfully',
  PURCHASE_COMPLETED: 'Purchase completed successfully',
  GIFT_SENT: 'Gift sent successfully',
  ACHIEVEMENT_UNLOCKED: 'Achievement unlocked!'
} as const;

// Game Assets
export const ASSETS = {
  IMAGES: {
    PLAYER: {
      DEFAULT: 'player_default.png',
      JUMPING: 'player_jumping.png',
      FALLING: 'player_falling.png'
    },
    PLATFORMS: {
      NORMAL: 'platform_normal.png',
      SPRING: 'platform_spring.png',
      MOVING: 'platform_moving.png',
      BREAKING: 'platform_breaking.png',
      ICE: 'platform_ice.png'
    },
    POWER_UPS: {
      DOUBLE_JUMP: 'powerup_double_jump.png',
      SHIELD: 'powerup_shield.png',
      MAGNET: 'powerup_magnet.png',
      BOOST: 'powerup_boost.png'
    },
    UI: {
      BACKGROUND: 'ui_background.jpg',
      BUTTON: 'ui_button.png',
      PANEL: 'ui_panel.png',
      ICON_SOUND: 'ui_icon_sound.png',
      ICON_MUSIC: 'ui_icon_music.png'
    }
  },
  
  SOUNDS: {
    JUMP: 'jump.mp3',
    LAND: 'land.mp3',
    POWER_UP: 'powerup.mp3',
    ACHIEVEMENT: 'achievement.mp3',
    GAME_OVER: 'gameover.mp3',
    BUTTON_CLICK: 'button_click.mp3'
  },
  
  MUSIC: {
    GAMEPLAY: 'music_gameplay.mp3',
    MENU: 'music_menu.mp3'
  }
} as const;

// Achievement Definitions
export const ACHIEVEMENTS = {
  FIRST_JUMP: {
    id: 'first_jump',
    name: '第一次跳跃',
    description: '完成你的第一次跳跃',
    type: 'progression',
    criteria: { metric: 'jumps', threshold: 1 },
    reward: { coins: 10 },
    rarity: 'common'
  },
  
  HIGH_SCORE_100: {
    id: 'high_score_100',
    name: '破百',
    description: '获得100分',
    type: 'score',
    criteria: { metric: 'high_score', threshold: 100 },
    reward: { coins: 50 },
    rarity: 'common'
  },
  
  HIGH_SCORE_1000: {
    id: 'high_score_1000',
    name: '千分达人',
    description: '获得1000分',
    type: 'score',
    criteria: { metric: 'high_score', threshold: 1000 },
    reward: { coins: 200 },
    rarity: 'rare'
  },
  
  DAILY_PLAYER: {
    id: 'daily_player',
    name: '每日一跳',
    description: '连续7天每天至少玩一局',
    type: 'consistency',
    criteria: { metric: 'daily_streak', threshold: 7 },
    reward: { coins: 100 },
    rarity: 'rare'
  },
  
  SOCIAL_BUTTERFLY: {
    id: 'social_butterfly',
    name: '社交达人',
    description: '添加10个好友',
    type: 'social',
    criteria: { metric: 'friends_count', threshold: 10 },
    reward: { coins: 75 },
    rarity: 'common'
  },
  
  CHALLENGER: {
    id: 'challenger',
    name: '挑战者',
    description: '赢得10场好友挑战',
    type: 'social',
    criteria: { metric: 'challenges_won', threshold: 10 },
    reward: { coins: 150 },
    rarity: 'rare'
  },
  
  PERFECTIONIST: {
    id: 'perfectionist',
    name: '完美主义者',
    description: '连续完美着陆20次',
    type: 'progression',
    criteria: { metric: 'perfect_landings_streak', threshold: 20 },
    reward: { coins: 300 },
    rarity: 'epic'
  }
} as const;

// Daily Rewards Configuration
export const DAILY_REWARDS = [
  { day: 1, type: 'coins', amount: 50 },
  { day: 2, type: 'coins', amount: 75 },
  { day: 3, type: 'power_up', amount: 1, itemId: 'double_jump' },
  { day: 4, type: 'coins', amount: 100 },
  { day: 5, type: 'power_up', amount: 2, itemId: 'shield' },
  { day: 6, type: 'coins', amount: 150 },
  { day: 7, type: 'skin', amount: 1, itemId: 'golden_jumper' }
] as const;

// WeChat Configuration
export const WECHAT_CONFIG = {
  API_BASE_URL: 'https://api.weixin.qq.com',
  MINIPROGRAM_BASE_URL: 'https://api.weixin.qq.com/sns/jscode2session',
  PAY_BASE_URL: 'https://api.mch.weixin.qq.com/pay/unifiedorder',
  
  SHARE_TEMPLATES: {
    HIGH_SCORE: {
      title: '我在跳跳乐中得了{score}分！',
      desc: '快来挑战我的记录吧！',
      imageUrl: '/images/share/high_score.jpg'
    },
    ACHIEVEMENT: {
      title: '我解锁了新成就：{achievement}！',
      desc: '一起来跳跳乐挑战更多成就吧！',
      imageUrl: '/images/share/achievement.jpg'
    },
    CHALLENGE: {
      title: '朋友向你发起挑战！',
      desc: '目标分数：{score}分，你能做到吗？',
      imageUrl: '/images/share/challenge.jpg'
    }
  }
} as const;

// Rate Limiting
export const RATE_LIMITS = {
  LOGIN: { windowMs: 15 * 60 * 1000, max: 5 }, // 5 attempts per 15 minutes
  GAME_SESSION: { windowMs: 60 * 1000, max: 10 }, // 10 sessions per minute
  FRIEND_REQUEST: { windowMs: 60 * 60 * 1000, max: 20 }, // 20 requests per hour
  CHALLENGE: { windowMs: 60 * 60 * 1000, max: 50 }, // 50 challenges per hour
  PURCHASE: { windowMs: 60 * 60 * 1000, max: 10 }, // 10 purchases per hour
  API_GENERAL: { windowMs: 15 * 60 * 1000, max: 1000 } // 1000 requests per 15 minutes
} as const;

// Cache Keys and TTL
export const CACHE_KEYS = {
  USER_PROFILE: (userId: string) => `user:${userId}:profile`,
  USER_STATS: (userId: string) => `user:${userId}:stats`,
  LEADERBOARD: (type: string, timeframe: string) => `leaderboard:${type}:${timeframe}`,
  GAME_CONFIG: 'game:config',
  ACHIEVEMENTS: 'achievements:all',
  DAILY_REWARDS: (userId: string) => `user:${userId}:daily_rewards`,
  FRIEND_LIST: (userId: string) => `user:${userId}:friends`,
  USER_SESSION: (userId: string) => `session:${userId}`
} as const;

export const CACHE_TTL = {
  USER_PROFILE: 300, // 5 minutes
  USER_STATS: 60, // 1 minute
  LEADERBOARD: 300, // 5 minutes
  GAME_CONFIG: 3600, // 1 hour
  ACHIEVEMENTS: 1800, // 30 minutes
  DAILY_REWARDS: 1800, // 30 minutes
  FRIEND_LIST: 600, // 10 minutes
  USER_SESSION: 604800 // 7 days
} as const;

// Environment Configuration
export const ENV = {
  DEVELOPMENT: 'development',
  STAGING: 'staging',
  PRODUCTION: 'production'
} as const;

// Validation Rules
export const VALIDATION_RULES = {
  USERNAME: {
    MIN_LENGTH: 2,
    MAX_LENGTH: 20,
    PATTERN: /^[a-zA-Z0-9\u4e00-\u9fa5_-]+$/
  },
  
  SCORE: {
    MIN: 0,
    MAX: 999999
  },
  
  CHALLENGE_MESSAGE: {
    MAX_LENGTH: 100
  },
  
  GIFT_MESSAGE: {
    MAX_LENGTH: 50
  }
} as const;

export default {
  GAME_CONFIG,
  API_ENDPOINTS,
  ERROR_CODES,
  SUCCESS_MESSAGES,
  ASSETS,
  ACHIEVEMENTS,
  DAILY_REWARDS,
  WECHAT_CONFIG,
  RATE_LIMITS,
  CACHE_KEYS,
  CACHE_TTL,
  ENV,
  VALIDATION_RULES
};