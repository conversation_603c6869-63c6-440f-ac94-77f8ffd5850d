{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020", "DOM"], "allowJs": true, "outDir": "./dist", "rootDir": "./", "strict": true, "moduleResolution": "node", "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "incremental": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "baseUrl": ".", "paths": {"@wechat-doodle/shared": ["packages/shared/src/index"], "@wechat-doodle/shared/*": ["packages/shared/src/*"], "@/*": ["apps/*/src/*"]}}, "include": ["apps/**/*", "packages/**/*"], "exclude": ["node_modules", "dist", "build", "**/*.test.ts", "**/*.spec.ts"], "references": [{"path": "./apps/client"}, {"path": "./apps/api"}, {"path": "./packages/shared"}]}