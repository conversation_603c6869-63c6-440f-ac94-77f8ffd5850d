import 'jest-canvas-mock';

// Mock WeChat API
(global as any).wx = {
  onShow: jest.fn(),
  onHide: jest.fn(),
  onMemoryWarning: jest.fn(),
  onAudioInterruptionBegin: jest.fn(),
  onAudioInterruptionEnd: jest.fn(),
  getSystemInfo: jest.fn(),
  getSystemInfoSync: jest.fn(() => ({
    screenWidth: 375,
    screenHeight: 667,
    pixelRatio: 2,
    platform: 'android',
    system: 'Android 10',
    brand: 'xiaomi',
    model: 'Redmi Note 8'
  })),
  vibrateShort: jest.fn(),
  vibrateLong: jest.fn()
};

// Mock global functions
(global as any).window = {
  ...global.window,
  updateLoadingProgress: jest.fn(),
  hideLoading: jest.fn(),
  showError: jest.fn(),
  gc: jest.fn()
};

// Mock Phaser 3 globally
(global as any).Phaser = {
  AUTO: 'AUTO',
  CANVAS: 'CANVAS',
  Scale: {
    FIT: 'FIT',
    CENTER_BOTH: 'CENTER_BOTH'
  },
  Types: {
    Core: {
      GameConfig: {}
    }
  }
};

// Mock Phaser 3
jest.mock('phaser', () => ({
  AUTO: 'AUTO',
  CANVAS: 'CANVAS',
  Scale: {
    FIT: 'FIT',
    CENTER_BOTH: 'CENTER_BOTH'
  },
  Types: {
    Core: {
      GameConfig: {}
    }
  },
  Game: jest.fn().mockImplementation(() => ({
    scene: {
      add: jest.fn(),
      getScene: jest.fn()
    },
    events: {
      once: jest.fn(),
      on: jest.fn()
    },
    isRunning: true,
    destroy: jest.fn()
  })),
  Scene: jest.fn(),
  Physics: {
    Arcade: {
      Sprite: jest.fn().mockImplementation(() => ({
        setPosition: jest.fn(),
        setVelocity: jest.fn(),
        setSize: jest.fn(),
        body: {
          setSize: jest.fn(),
          setCollideWorldBounds: jest.fn(),
          setGravityY: jest.fn(),
          setDragX: jest.fn(),
          setMaxVelocityX: jest.fn(),
          setMaxVelocityY: jest.fn(),
          setBounce: jest.fn(),
          velocity: { x: 0, y: 0 },
          blocked: { down: false }
        }
      }))
    }
  }
}));

// Setup test environment
beforeEach(() => {
  jest.clearAllMocks();
});