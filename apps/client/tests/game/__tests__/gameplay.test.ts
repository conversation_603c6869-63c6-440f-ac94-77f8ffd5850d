import { Player } from '../../../src/game/entities/Player';
import { Platform } from '../../../src/game/entities/Platform';
import { PlatformGenerator } from '../../../src/game/systems/PlatformGenerator';
import { GAME_CONSTANTS } from '../../../src/game/config/GameConfig';
import { PlayerState, PlatformType } from '../../../src/types/game';

// Mock Phaser Scene
const mockScene = {
  add: {
    existing: jest.fn()
  },
  physics: {
    add: {
      existing: jest.fn()
    },
    world: {
      scene: {}
    }
  },
  time: {
    now: 1000
  }
} as any;

describe('Core Jump Mechanics - Gameplay Tests', () => {
  
  describe('Player Physics System', () => {
    let player: Player;

    beforeEach(() => {
      player = new Player(mockScene, 100, 100);
    });

    test('should initialize with correct default state', () => {
      expect(player.getState()).toBe(PlayerState.IDLE);
      expect(player.isGrounded()).toBe(false);
      expect(player.getVelocity()).toEqual({ x: 0, y: 0 });
    });

    test('should apply gravity correctly', () => {
      const deltaTime = 16; // 16ms = 60fps
      player.update(deltaTime);
      
      const velocity = player.getVelocity();
      const expectedGravityEffect = GAME_CONSTANTS.PHYSICS.GRAVITY * (deltaTime / 1000);
      
      expect(velocity.y).toBeCloseTo(expectedGravityEffect, 1);
    });

    test('should execute jump with correct velocity', () => {
      const jumpSuccess = player.jump();
      
      expect(jumpSuccess).toBe(false); // Should fail when not grounded
      
      // Simulate grounding
      (player as any).grounded = true;
      const groundedJumpSuccess = player.jump();
      
      expect(groundedJumpSuccess).toBe(true);
      expect(player.getState()).toBe(PlayerState.JUMPING);
      expect(player.getVelocity().y).toBe(GAME_CONSTANTS.PHYSICS.JUMP_VELOCITY);
    });

    test('should handle horizontal movement correctly', () => {
      player.setHorizontalInput(1); // Move right
      player.update(16);
      
      const velocity = player.getVelocity();
      expect(velocity.x).toBeGreaterThan(0);
      expect(velocity.x).toBeLessThanOrEqual(GAME_CONSTANTS.PLAYER.MAX_HORIZONTAL_SPEED);
    });

    test('should transition states correctly', () => {
      // Start idle
      expect(player.getState()).toBe(PlayerState.IDLE);
      
      // Simulate jump
      (player as any).grounded = true;
      player.jump();
      expect(player.getState()).toBe(PlayerState.JUMPING);
      
      // Simulate falling
      (player as any).velocity.y = 100;
      player.update(16);
      expect(player.getState()).toBe(PlayerState.FALLING);
    });

    test('should respect maximum fall speed', () => {
      // Simulate long fall
      for (let i = 0; i < 100; i++) {
        player.update(16);
      }
      
      const velocity = player.getVelocity();
      expect(velocity.y).toBeLessThanOrEqual(GAME_CONSTANTS.PHYSICS.MAX_FALL_SPEED);
    });

    test('should handle platform jump with bounce multiplier', () => {
      const bounceVelocity = -600; // Spring platform bounce
      player.platformJump(bounceVelocity);
      
      expect(player.getState()).toBe(PlayerState.JUMPING);
      expect(player.getVelocity().y).toBe(bounceVelocity);
    });
  });

  describe('Platform Generation System', () => {
    let platformGenerator: PlatformGenerator;
    let mockPhysics: any;
    let platformPool: Platform[];

    beforeEach(() => {
      mockPhysics = {
        world: {
          scene: mockScene
        }
      };
      platformPool = [];
      platformGenerator = new PlatformGenerator(mockPhysics, platformPool);
    });

    test('should initialize with correct default values', () => {
      expect(platformGenerator.getDifficulty()).toBe(1.0);
      expect(platformGenerator.getGeneratedCount()).toBe(0);
    });

    test('should generate platforms with correct spacing', () => {
      const platforms = platformGenerator.generatePlatformsInRange(500, 200);
      
      // Validate platform spacing
      const isValidSpacing = platformGenerator.validatePlatformSpacing(platforms);
      expect(isValidSpacing).toBe(true);
      
      // Check individual gaps
      for (let i = 1; i < platforms.length; i++) {
        const gap = Math.abs(platforms[i-1].y - platforms[i].y);
        expect(gap).toBeGreaterThanOrEqual(GAME_CONSTANTS.PLATFORMS.MIN_GAP);
        expect(gap).toBeLessThanOrEqual(GAME_CONSTANTS.PLATFORMS.MAX_GAP);
      }
    });

    test('should generate different platform types', () => {
      const mockPhysics = {
        world: { scene: mockScene }
      } as any;
      const mockPlatformPool: Platform[] = [];
      const generator = new PlatformGenerator(mockPhysics, mockPlatformPool);
      
      const platforms = generator.generatePlatformsInRange(1000, 0);
      
      const platformTypes = platforms.map(p => p.type);
      const uniqueTypes = new Set(platformTypes);
      
      // Should have at least normal platforms
      expect(uniqueTypes.has(PlatformType.NORMAL)).toBe(true);
      
      // With enough platforms, should have some variety
      if (platforms.length > 10) {
        expect(uniqueTypes.size).toBeGreaterThan(1);
      }
    });

    test('should increase difficulty over time', () => {
      const initialDifficulty = platformGenerator.getDifficulty();
      
      // Generate many platforms to trigger difficulty increase
      platformGenerator.generatePlatformsInRange(2000, 0);
      
      const finalDifficulty = platformGenerator.getDifficulty();
      expect(finalDifficulty).toBeGreaterThan(initialDifficulty);
    });

    test('should respect maximum difficulty limit', () => {
      // Force high difficulty
      platformGenerator.setDifficulty(5.0); // Above max
      
      expect(platformGenerator.getDifficulty()).toBeLessThanOrEqual(2.0);
    });
  });

  describe('Platform Types and Behaviors', () => {
    test('should create normal platform correctly', () => {
      const platform = new Platform(mockScene, 100, 100, PlatformType.NORMAL);
      
      expect(platform.type).toBe(PlatformType.NORMAL);
      expect(platform.getBounceMultiplier()).toBe(1.0);
      expect(platform.isActiveState()).toBe(true);
    });

    test('should create spring platform with enhanced bounce', () => {
      const platform = new Platform(mockScene, 100, 100, PlatformType.SPRING);
      
      expect(platform.type).toBe(PlatformType.SPRING);
      expect(platform.getBounceMultiplier()).toBe(1.5);
    });

    test('should handle moving platform behavior', () => {
      const platform = new Platform(mockScene, 100, 100, PlatformType.MOVING);
      
      expect(platform.type).toBe(PlatformType.MOVING);
      expect(platform.isMoving).toBe(true);
      expect(platform.movementSpeed).toBeDefined();
      expect(platform.movementRange).toBeDefined();
    });

    test('should handle breaking platform behavior', () => {
      const platform = new Platform(mockScene, 100, 100, PlatformType.BREAKING);
      const mockPlayer = { /* mock player object */ };
      
      const shouldBounce = platform.onPlayerCollision(mockPlayer);
      expect(shouldBounce).toBe(true);
      
      // After collision, platform should start breaking sequence
      // (implementation detail - would need to test internal state)
    });

    test('should handle platform reset for object pooling', () => {
      const platform = new Platform(mockScene, 100, 100, PlatformType.SPRING);
      
      // Deactivate platform
      platform.deactivate();
      expect(platform.isActiveState()).toBe(false);
      
      // Reset platform
      platform.reset(200, 200, PlatformType.NORMAL);
      expect(platform.isActiveState()).toBe(true);
      expect(platform.x).toBe(200);
      expect(platform.y).toBe(200);
      expect(platform.type).toBe(PlatformType.NORMAL);
    });
  });

  describe('Physics Constants Validation', () => {
    test('should have correct physics constants', () => {
      expect(GAME_CONSTANTS.PHYSICS.GRAVITY).toBe(980);
      expect(GAME_CONSTANTS.PHYSICS.JUMP_VELOCITY).toBe(-450);
      expect(GAME_CONSTANTS.PHYSICS.MAX_FALL_SPEED).toBe(800);
    });

    test('should have correct platform constants', () => {
      expect(GAME_CONSTANTS.PLATFORMS.BASE_WIDTH).toBe(80);
      expect(GAME_CONSTANTS.PLATFORMS.MIN_GAP).toBe(30);
      expect(GAME_CONSTANTS.PLATFORMS.MAX_GAP).toBe(120);
    });

    test('should have correct player constants', () => {
      expect(GAME_CONSTANTS.PLAYER.SIZE.width).toBe(32);
      expect(GAME_CONSTANTS.PLAYER.SIZE.height).toBe(32);
      expect(GAME_CONSTANTS.PLAYER.MAX_HORIZONTAL_SPEED).toBe(300);
    });
  });

  describe('Performance Requirements', () => {
    test('should generate platforms within performance budget', () => {
      const mockPhysics = {
        world: { scene: mockScene }
      } as any;
      const mockPlatformPool: Platform[] = [];
      const generator = new PlatformGenerator(mockPhysics, mockPlatformPool);
      
      const startTime = Date.now();
      const platforms = generator.generatePlatformsInRange(500, 0);
      const endTime = Date.now();
      
      const generationTime = endTime - startTime;
      
      // Should generate platforms quickly (under 10ms for small batch)
      expect(generationTime).toBeLessThan(10);
      expect(platforms.length).toBeGreaterThan(0);
    });

    test('should maintain 60fps target frame time budget', () => {
      const player = new Player(mockScene, 100, 100);
      const targetFrameTime = 1000 / 60; // ~16.67ms
      
      const startTime = Date.now();
      
      // Simulate one frame update
      player.update(16);
      
      const endTime = Date.now();
      const updateTime = endTime - startTime;
      
      // Physics update should be well under frame budget
      expect(updateTime).toBeLessThan(targetFrameTime / 2);
    });
  });

  describe('Edge Cases and Error Handling', () => {
    test('should handle invalid jump attempts', () => {
      const player = new Player(mockScene, 100, 100);
      
      // Try to jump when not grounded
      const jumpResult = player.jump();
      expect(jumpResult).toBe(false);
      expect(player.getState()).toBe(PlayerState.IDLE);
    });

    test('should handle extreme velocity values', () => {
      const player = new Player(mockScene, 100, 100);
      
      // Set extreme horizontal input
      player.setHorizontalInput(999);
      player.update(16);
      
      const velocity = player.getVelocity();
      expect(velocity.x).toBeLessThanOrEqual(GAME_CONSTANTS.PLAYER.MAX_HORIZONTAL_SPEED);
    });

    test('should handle platform generation at boundaries', () => {
      const mockPhysics = {
        world: { scene: mockScene }
      } as any;
      const mockPlatformPool: Platform[] = [];
      const generator = new PlatformGenerator(mockPhysics, mockPlatformPool);
      
      const platforms = generator.generatePlatformsInRange(0, -10);
      
      // Should handle edge case gracefully
      expect(platforms.length).toBeGreaterThanOrEqual(0);
    });

    test('should handle platform collision with inactive platforms', () => {
      const platform = new Platform(mockScene, 100, 100, PlatformType.NORMAL);
      platform.deactivate();
      
      const mockPlayer = {};
      const shouldBounce = platform.onPlayerCollision(mockPlayer);
      
      expect(shouldBounce).toBe(false);
    });
  });
});