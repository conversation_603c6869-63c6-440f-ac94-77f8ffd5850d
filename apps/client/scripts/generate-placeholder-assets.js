#!/usr/bin/env node

/**
 * Generate placeholder assets for WeChat MiniGame demonstration
 * Creates simple colored rectangles and circles as sprite placeholders
 */

const fs = require('fs');
const path = require('path');
const { createCanvas } = require('canvas');

// Asset definitions
const ASSETS = {
  // Player sprites
  'player-idle': { width: 32, height: 32, color: '#4A90E2', shape: 'circle' },
  'player-jumping': { width: 32, height: 32, color: '#5BA0F2', shape: 'circle' },
  'player-falling': { width: 32, height: 32, color: '#357ABD', shape: 'circle' },
  
  // Platform sprites
  'platform-normal': { width: 80, height: 16, color: '#8B4513', shape: 'rect' },
  'platform-spring': { width: 80, height: 20, color: '#FF6B6B', shape: 'rect' },
  'platform-moving': { width: 80, height: 16, color: '#4ECDC4', shape: 'rect' },
  'platform-breaking': { width: 80, height: 16, color: '#FFE66D', shape: 'rect' },
  'platform-disappearing': { width: 80, height: 16, color: '#A8E6CF', shape: 'rect' },
  
  // Background elements
  'background': { width: 375, height: 667, color: '#87CEEB', shape: 'rect' },
  'cloud-1': { width: 60, height: 30, color: '#FFFFFF', shape: 'ellipse' },
  'cloud-2': { width: 80, height: 40, color: '#F0F8FF', shape: 'ellipse' },
  
  // UI elements
  'button-play': { width: 120, height: 40, color: '#4A90E2', shape: 'roundRect' },
  'button-pause': { width: 40, height: 40, color: '#FF6B6B', shape: 'roundRect' },
  
  // Power-ups
  'powerup-jetpack': { width: 24, height: 24, color: '#FF4757', shape: 'rect' },
  'powerup-spring': { width: 24, height: 24, color: '#2ED573', shape: 'rect' },
  'powerup-shield': { width: 24, height: 24, color: '#FFA502', shape: 'circle' }
};

// Create assets directory if it doesn't exist
const assetsDir = path.join(__dirname, '..', 'public', 'assets', 'images');
if (!fs.existsSync(assetsDir)) {
  fs.mkdirSync(assetsDir, { recursive: true });
}

// Generate each asset
Object.entries(ASSETS).forEach(([name, config]) => {
  const canvas = createCanvas(config.width, config.height);
  const ctx = canvas.getContext('2d');
  
  // Clear canvas with transparent background
  ctx.clearRect(0, 0, config.width, config.height);
  
  // Set fill color
  ctx.fillStyle = config.color;
  
  // Draw shape based on type
  switch (config.shape) {
    case 'rect':
      ctx.fillRect(0, 0, config.width, config.height);
      break;
      
    case 'circle':
      const radius = Math.min(config.width, config.height) / 2;
      ctx.beginPath();
      ctx.arc(config.width / 2, config.height / 2, radius - 2, 0, 2 * Math.PI);
      ctx.fill();
      break;
      
    case 'ellipse':
      ctx.beginPath();
      ctx.ellipse(config.width / 2, config.height / 2, config.width / 2 - 2, config.height / 2 - 2, 0, 0, 2 * Math.PI);
      ctx.fill();
      break;
      
    case 'roundRect':
      const cornerRadius = 8;
      ctx.beginPath();
      ctx.roundRect(0, 0, config.width, config.height, cornerRadius);
      ctx.fill();
      break;
      
    default:
      ctx.fillRect(0, 0, config.width, config.height);
  }
  
  // Add border for better visibility
  ctx.strokeStyle = '#000000';
  ctx.lineWidth = 1;
  ctx.strokeRect(0, 0, config.width, config.height);
  
  // Save as PNG
  const buffer = canvas.toBuffer('image/png');
  const filename = path.join(assetsDir, `${name}.png`);
  fs.writeFileSync(filename, buffer);
  
  console.log(`Generated: ${name}.png (${config.width}x${config.height})`);
});

// Create a simple favicon
const faviconCanvas = createCanvas(32, 32);
const faviconCtx = faviconCanvas.getContext('2d');
faviconCtx.fillStyle = '#4A90E2';
faviconCtx.beginPath();
faviconCtx.arc(16, 16, 14, 0, 2 * Math.PI);
faviconCtx.fill();
faviconCtx.strokeStyle = '#357ABD';
faviconCtx.lineWidth = 2;
faviconCtx.stroke();

const faviconBuffer = faviconCanvas.toBuffer('image/png');
fs.writeFileSync(path.join(assetsDir, 'favicon.png'), faviconBuffer);
console.log('Generated: favicon.png (32x32)');

console.log(`\nGenerated ${Object.keys(ASSETS).length + 1} placeholder assets successfully!`);
console.log('Assets saved to:', assetsDir);
