#!/usr/bin/env node

/**
 * Verify WeChat MiniGame Build
 * This script checks that all required files are present and properly configured
 */

const fs = require('fs');
const path = require('path');

const DIST_DIR = path.join(__dirname, '..', 'dist');
const REQUIRED_FILES = [
  'app.js',
  'game.js',
  'game.html',
  'game.json',
  'project.config.json'
];

const REQUIRED_ASSETS = [
  'assets/images/player-idle.png',
  'assets/images/platform-normal.png',
  'assets/images/background.png',
  'assets/audio/jump.mp3'
];

console.log('🔍 Verifying WeChat MiniGame build...\n');

// Check if dist directory exists
if (!fs.existsSync(DIST_DIR)) {
  console.error('❌ Error: dist/ directory not found. Run "npm run build" first.');
  process.exit(1);
}

console.log('✅ dist/ directory found');

// Check required files
let missingFiles = [];
REQUIRED_FILES.forEach(file => {
  const filePath = path.join(DIST_DIR, file);
  if (fs.existsSync(filePath)) {
    const stats = fs.statSync(filePath);
    console.log(`✅ ${file} (${(stats.size / 1024).toFixed(1)}KB)`);
  } else {
    console.log(`❌ ${file} - MISSING`);
    missingFiles.push(file);
  }
});

// Check required assets
REQUIRED_ASSETS.forEach(asset => {
  const assetPath = path.join(DIST_DIR, asset);
  if (fs.existsSync(assetPath)) {
    console.log(`✅ ${asset}`);
  } else {
    console.log(`❌ ${asset} - MISSING`);
    missingFiles.push(asset);
  }
});

// Validate configuration files
console.log('\n📋 Validating configuration files...');

// Check game.json
try {
  const gameJsonPath = path.join(DIST_DIR, 'game.json');
  const gameJson = JSON.parse(fs.readFileSync(gameJsonPath, 'utf8'));
  
  if (gameJson.renderer === 'canvas2d') {
    console.log('✅ game.json: Canvas2D renderer configured');
  } else {
    console.log('⚠️  game.json: Renderer should be "canvas2d" for WeChat compatibility');
  }
  
  if (gameJson.deviceOrientation === 'portrait') {
    console.log('✅ game.json: Portrait orientation configured');
  } else {
    console.log('⚠️  game.json: Device orientation should be "portrait"');
  }
} catch (error) {
  console.log('❌ game.json: Invalid JSON format');
}

// Check project.config.json
try {
  const projectConfigPath = path.join(DIST_DIR, 'project.config.json');
  const projectConfig = JSON.parse(fs.readFileSync(projectConfigPath, 'utf8'));
  
  if (projectConfig.compileType === 'miniGame') {
    console.log('✅ project.config.json: MiniGame compile type configured');
  } else {
    console.log('❌ project.config.json: compileType should be "miniGame"');
  }
  
  if (projectConfig.appid) {
    console.log(`✅ project.config.json: AppID configured (${projectConfig.appid})`);
  } else {
    console.log('⚠️  project.config.json: No AppID configured');
  }
} catch (error) {
  console.log('❌ project.config.json: Invalid JSON format');
}

// Check bundle size
const gameJsPath = path.join(DIST_DIR, 'game.js');
if (fs.existsSync(gameJsPath)) {
  const gameJsSize = fs.statSync(gameJsPath).size;
  const sizeMB = (gameJsSize / (1024 * 1024)).toFixed(1);
  
  if (gameJsSize < 20 * 1024 * 1024) { // 20MB limit
    console.log(`✅ Bundle size: ${sizeMB}MB (within WeChat 20MB limit)`);
  } else {
    console.log(`⚠️  Bundle size: ${sizeMB}MB (exceeds WeChat 20MB limit)`);
  }
}

// Summary
console.log('\n📊 Build Verification Summary:');
if (missingFiles.length === 0) {
  console.log('✅ All required files are present');
  console.log('✅ Build verification passed!');
  console.log('\n🚀 Ready for WeChat Developer Tools import');
  console.log('\nNext steps:');
  console.log('1. Open WeChat Developer Tools');
  console.log('2. Import project from: apps/client/dist/');
  console.log('3. Use AppID: wx3587a6c5a3c4f177');
  console.log('4. Select project type: MiniGame');
  console.log('\nSee WECHAT_SETUP.md for detailed instructions.');
} else {
  console.log(`❌ ${missingFiles.length} files are missing:`);
  missingFiles.forEach(file => console.log(`   - ${file}`));
  console.log('\nRun "npm run build" to generate missing files.');
  process.exit(1);
}
