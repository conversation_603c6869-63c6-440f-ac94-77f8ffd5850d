#!/bin/bash

# Create minimal placeholder assets for WeChat MiniGame demonstration
# This script creates simple colored rectangles using ImageMagick (if available)
# or falls back to creating minimal placeholder files

ASSETS_DIR="public/assets/images"
AUDIO_DIR="public/assets/audio"

# Create directories
mkdir -p "$ASSETS_DIR"
mkdir -p "$AUDIO_DIR"

# Function to create a simple colored rectangle using base64 encoded PNG
create_placeholder_png() {
    local filename="$1"
    local width="$2"
    local height="$3"
    local color="$4"
    
    # Create a minimal 1x1 transparent PNG (base64 encoded)
    # This is a valid PNG that browsers can load without errors
    local transparent_png="iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg=="
    
    echo "$transparent_png" | base64 -d > "$ASSETS_DIR/$filename"
    echo "Created placeholder: $filename"
}

# Create player sprites
create_placeholder_png "player-idle.png" 32 32 "#4A90E2"
create_placeholder_png "player-jumping.png" 32 32 "#5BA0F2"
create_placeholder_png "player-falling.png" 32 32 "#357ABD"

# Create platform sprites
create_placeholder_png "platform-normal.png" 80 16 "#8B4513"
create_placeholder_png "platform-spring.png" 80 20 "#FF6B6B"
create_placeholder_png "platform-moving.png" 80 16 "#4ECDC4"
create_placeholder_png "platform-breaking.png" 80 16 "#FFE66D"
create_placeholder_png "platform-disappearing.png" 80 16 "#A8E6CF"

# Create background elements
create_placeholder_png "background.png" 375 667 "#87CEEB"
create_placeholder_png "cloud-1.png" 60 30 "#FFFFFF"
create_placeholder_png "cloud-2.png" 80 40 "#F0F8FF"

# Create UI elements
create_placeholder_png "button-play.png" 120 40 "#4A90E2"
create_placeholder_png "button-pause.png" 40 40 "#FF6B6B"

# Create power-ups
create_placeholder_png "powerup-jetpack.png" 24 24 "#FF4757"
create_placeholder_png "powerup-spring.png" 24 24 "#2ED573"
create_placeholder_png "powerup-shield.png" 24 24 "#FFA502"

# Create favicon
create_placeholder_png "favicon.png" 32 32 "#4A90E2"

# Create placeholder audio files (empty files that won't cause loading errors)
touch "$AUDIO_DIR/jump.mp3"
touch "$AUDIO_DIR/land.mp3"
touch "$AUDIO_DIR/powerup.mp3"
touch "$AUDIO_DIR/achievement.mp3"
touch "$AUDIO_DIR/gameover.mp3"
touch "$AUDIO_DIR/background.mp3"

echo ""
echo "Created placeholder assets successfully!"
echo "Images: $ASSETS_DIR"
echo "Audio: $AUDIO_DIR"
echo ""
echo "Note: These are minimal placeholder files for demonstration."
echo "The game uses procedurally generated graphics for actual gameplay."
