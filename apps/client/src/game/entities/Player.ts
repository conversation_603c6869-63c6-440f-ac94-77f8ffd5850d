import { PlayerState, PlayerData } from '../../types/game';
import { GAME_CONSTANTS } from '../config/GameConfig';

export class Player extends Phaser.Physics.Arcade.Sprite {
  private state: PlayerState = PlayerState.IDLE;
  private velocity: { x: number; y: number } = { x: 0, y: 0 };
  private grounded: boolean = false;
  private jumpCooldown: number = 0;
  private horizontalInput: number = 0;
  
  // Animation state
  private animationState = {
    current: 'idle',
    frame: 0,
    speed: 1
  };

  // Physics properties
  private readonly physics = {
    gravity: GAME_CONSTANTS.PHYSICS.GRAVITY,
    jumpVelocity: GAME_CONSTANTS.PLAYER.JUMP_VELOCITY,
    horizontalSpeed: GAME_CONSTANTS.PLAYER.MAX_HORIZONTAL_SPEED,
    acceleration: GAME_CONSTANTS.PLAYER.ACCELERATION,
    friction: GAME_CONSTANTS.PLAYER.FRICTION,
    maxFallSpeed: GAME_CONSTANTS.PHYSICS.MAX_FALL_SPEED
  };

  // State tracking
  private previousY: number = 0;
  private isJumping: boolean = false;
  private jumpStartTime: number = 0;
  private consecutiveJumps: number = 0;

  constructor(scene: Phaser.Scene, x: number, y: number) {
    super(scene, x, y, 'player');
    
    // Add to scene and physics
    scene.add.existing(this);
    scene.physics.add.existing(this);

    // Configure physics body
    this.setupPhysics();
    
    // Initialize state
    this.initializeState();
    
    // Set initial position tracking
    this.previousY = y;

    console.log(`Player created at position (${x}, ${y})`);
  }

  private setupPhysics(): void {
    if (!this.body) return;

    const body = this.body as Phaser.Physics.Arcade.Body;
    
    // Set physics body size based on sprite
    body.setSize(
      GAME_CONSTANTS.PLAYER.SIZE.width,
      GAME_CONSTANTS.PLAYER.SIZE.height
    );
    
    // Configure physics properties
    body.setCollideWorldBounds(true);
    body.setGravityY(0); // We'll handle gravity manually for better control
    body.setDragX(this.physics.friction * 100);
    body.setMaxVelocityX(this.physics.horizontalSpeed);
    body.setMaxVelocityY(this.physics.maxFallSpeed);
    
    // Set bounce properties
    body.setBounce(0, 0);
    
    console.log('Player physics configured');
  }

  private initializeState(): void {
    this.state = PlayerState.IDLE;
    this.velocity = { x: 0, y: 0 };
    this.grounded = false;
    this.isJumping = false;
    this.jumpCooldown = 0;
    this.consecutiveJumps = 0;
  }

  public update(delta: number): void {
    if (!this.body) return;

    const deltaSeconds = delta / 1000;
    
    // Update physics
    this.updatePhysics(deltaSeconds);
    
    // Update state machine
    this.updateState(deltaSeconds);
    
    // Update animation
    this.updateAnimation(deltaSeconds);
    
    // Update jump cooldown
    if (this.jumpCooldown > 0) {
      this.jumpCooldown -= deltaSeconds;
    }
    
    // Track position for state changes
    this.previousY = this.y;
  }

  private updatePhysics(deltaSeconds: number): void {
    const body = this.body as Phaser.Physics.Arcade.Body;
    
    // WeChat-optimized physics with fixed timestep
    const fixedDeltaSeconds = GAME_CONSTANTS.PERFORMANCE.PHYSICS_STEP_SIZE;
    
    // Apply custom gravity with WeChat optimization
    this.velocity.y += this.physics.gravity * fixedDeltaSeconds;
    
    // Cap falling speed
    if (this.velocity.y > this.physics.maxFallSpeed) {
      this.velocity.y = this.physics.maxFallSpeed;
    }
    
    // Apply horizontal movement with WeChat touch sensitivity
    if (this.horizontalInput !== 0) {
      // Enhanced touch sensitivity for WeChat
      const touchSensitivity = GAME_CONSTANTS.WECHAT.TOUCH_SENSITIVITY;
      const adjustedInput = this.horizontalInput * touchSensitivity;
      
      // Accelerate in input direction
      this.velocity.x += adjustedInput * this.physics.acceleration * fixedDeltaSeconds;
      
      // Cap horizontal speed
      if (Math.abs(this.velocity.x) > this.physics.horizontalSpeed) {
        this.velocity.x = Math.sign(this.velocity.x) * this.physics.horizontalSpeed;
      }
    } else {
      // Apply enhanced friction for WeChat responsiveness
      const wechatFriction = this.physics.friction * 0.95; // Slightly more responsive
      this.velocity.x *= wechatFriction;
      
      // Stop very small movements to prevent jitter (larger threshold for WeChat)
      if (Math.abs(this.velocity.x) < 2) {
        this.velocity.x = 0;
      }
    }
    
    // Apply velocity to physics body with integer positions for WeChat
    const intX = Math.round(this.velocity.x);
    const intY = Math.round(this.velocity.y);
    body.setVelocity(intX, intY);
    
    // Handle screen wrapping (player goes off one side, appears on other)
    this.handleScreenWrapping();
  }

  private updateState(deltaSeconds: number): void {
    const wasGrounded = this.grounded;
    
    // Check if grounded (this will be set by collision detection)
    // For now, we'll use a simple ground check
    this.grounded = this.velocity.y >= 0 && this.body && this.body.blocked.down;
    
    // State transitions
    switch (this.state) {
      case PlayerState.IDLE:
        if (this.velocity.y < 0) {
          this.setState(PlayerState.JUMPING);
        } else if (this.velocity.y > 10) {
          this.setState(PlayerState.FALLING);
        }
        break;
        
      case PlayerState.JUMPING:
        if (this.velocity.y >= 0) {
          this.setState(PlayerState.FALLING);
        }
        break;
        
      case PlayerState.FALLING:
        if (this.grounded && !wasGrounded) {
          this.setState(PlayerState.LANDING);
          this.onLanding();
        }
        break;
        
      case PlayerState.LANDING:
        // Brief landing state, quickly transition to idle
        if (this.grounded) {
          this.setState(PlayerState.IDLE);
        }
        break;
    }
    
    // Update jumping state
    if (this.isJumping && this.velocity.y >= 0) {
      this.isJumping = false;
    }
  }

  private updateAnimation(deltaSeconds: number): void {
    // Simple animation state management
    let targetAnimation = 'idle';
    
    switch (this.state) {
      case PlayerState.JUMPING:
        targetAnimation = 'jumping';
        break;
      case PlayerState.FALLING:
        targetAnimation = 'falling';
        break;
      case PlayerState.LANDING:
        targetAnimation = 'landing';
        break;
      default:
        targetAnimation = 'idle';
        break;
    }
    
    // Update animation state
    if (this.animationState.current !== targetAnimation) {
      this.animationState.current = targetAnimation;
      this.animationState.frame = 0;
    }
    
    // Update animation frame (simple frame counter)
    this.animationState.frame += this.animationState.speed * deltaSeconds;
  }

  private handleScreenWrapping(): void {
    const worldWidth = GAME_CONSTANTS.WORLD.WIDTH;
    const padding = GAME_CONSTANTS.WORLD.BOUNDS_PADDING;
    
    if (this.x < -padding) {
      this.x = worldWidth + padding;
    } else if (this.x > worldWidth + padding) {
      this.x = -padding;
    }
  }

  private onLanding(): void {
    // Reset consecutive jumps when landing
    this.consecutiveJumps = 0;
    
    // Landing effects would go here (particles, sound, etc.)
    console.log('Player landed');
  }

  // Public interface methods
  public jump(): boolean {
    // Check if jump is possible
    if (this.jumpCooldown > 0 || this.state === PlayerState.DEAD) {
      return false;
    }
    
    // Only jump if grounded (classic Doodle Jump behavior)
    if (!this.grounded && !this.canDoubleJump()) {
      return false;
    }
    
    // Execute jump
    this.velocity.y = this.physics.jumpVelocity;
    this.setState(PlayerState.JUMPING);
    this.isJumping = true;
    this.grounded = false;
    this.jumpStartTime = this.scene.time.now;
    this.jumpCooldown = 0.1; // 100ms cooldown to prevent spam
    this.consecutiveJumps++;
    
    console.log('Player jumped with velocity:', this.physics.jumpVelocity);
    return true;
  }

  public platformJump(bounceVelocity?: number): void {
    // Special jump triggered by platform collision
    const jumpVel = bounceVelocity || this.physics.jumpVelocity;
    
    this.velocity.y = jumpVel;
    this.setState(PlayerState.JUMPING);
    this.isJumping = true;
    this.grounded = false;
    this.jumpStartTime = this.scene.time.now;
    
    console.log('Platform jump with velocity:', jumpVel);
  }

  public setHorizontalInput(input: number): void {
    // Normalize input to -1, 0, or 1
    this.horizontalInput = Math.max(-1, Math.min(1, input));
  }

  public setState(newState: PlayerState): void {
    if (this.state !== newState) {
      const oldState = this.state;
      this.state = newState;
      
      console.log(`Player state changed: ${oldState} -> ${newState}`);
      
      // State-specific actions
      this.onStateChanged(oldState, newState);
    }
  }

  private onStateChanged(oldState: PlayerState, newState: PlayerState): void {
    switch (newState) {
      case PlayerState.JUMPING:
        // Jumping state effects
        break;
        
      case PlayerState.FALLING:
        // Falling state effects
        break;
        
      case PlayerState.LANDING:
        // Landing state effects
        break;
        
      case PlayerState.DEAD:
        // Death state - stop all movement
        this.velocity.x = 0;
        this.velocity.y = 0;
        if (this.body) {
          (this.body as Phaser.Physics.Arcade.Body).setVelocity(0, 0);
        }
        break;
    }
  }

  private canDoubleJump(): boolean {
    // For now, double jump is disabled (classic Doodle Jump behavior)
    // This could be enabled via power-ups later
    return false;
  }

  // Collision handling
  public onPlatformCollision(platform: any): void {
    // Only bounce if falling down onto platform
    if (this.velocity.y > 0) {
      // Calculate bounce based on platform type
      let bounceVelocity = this.physics.jumpVelocity;
      
      // Platform-specific bounce logic would go here
      // For now, use standard jump velocity
      
      this.platformJump(bounceVelocity);
    }
  }

  // Getters for external systems
  public getState(): PlayerState {
    return this.state;
  }

  public getVelocity(): { x: number; y: number } {
    return { ...this.velocity };
  }

  public isGrounded(): boolean {
    return this.grounded;
  }

  public isMoving(): boolean {
    return Math.abs(this.velocity.x) > 1 || Math.abs(this.velocity.y) > 1;
  }

  public getPlayerData(): PlayerData {
    return {
      id: 'player-1', // In a full game, this would be dynamic
      position: { x: this.x, y: this.y },
      velocity: this.getVelocity(),
      state: this.state,
      skin: {
        id: 'default',
        name: 'Default',
        texture: 'player',
        animations: {},
        unlocked: true
      },
      animations: this.animationState,
      effects: [] // Power-up effects would go here
    };
  }

  // Reset player for game restart
  public reset(x: number, y: number): void {
    this.setPosition(x, y);
    this.initializeState();
    
    if (this.body) {
      (this.body as Phaser.Physics.Arcade.Body).setVelocity(0, 0);
    }
    
    this.previousY = y;
    console.log(`Player reset to position (${x}, ${y})`);
  }

  // Cleanup
  public destroy(): void {
    console.log('Player destroyed');
    super.destroy();
  }
}