import { PlatformType, Platform as PlatformInterface } from '../../types/game';
import { GAME_CONSTANTS } from '../config/GameConfig';

export class Platform extends Phaser.Physics.Arcade.Sprite implements PlatformInterface {
  public id: string;
  public type: PlatformType;
  public width: number;
  public isMoving?: boolean;
  public movementSpeed?: number;
  public movementRange?: number;

  // Movement tracking for moving platforms
  private initialX: number;
  private movementDirection: number = 1;
  private movementTimer: number = 0;

  // Platform state
  private isActive: boolean = true;
  private bounceMultiplier: number = 1.0;

  constructor(scene: Phaser.Scene, x: number, y: number, type: PlatformType = PlatformType.NORMAL) {
    super(scene, x, y, Platform.getTextureForType(type));
    
    this.id = `platform-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    this.type = type;
    this.width = GAME_CONSTANTS.PLATFORMS.BASE_WIDTH;
    this.initialX = x;

    // Add to scene and physics
    scene.add.existing(this);
    scene.physics.add.existing(this);

    // Configure physics body
    this.setupPhysics();
    
    // Configure platform-specific properties
    this.configurePlatformType();

    console.log(`Platform created: ${this.type} at (${x}, ${y})`);
  }

  private static getTextureForType(type: PlatformType): string {
    switch (type) {
      case PlatformType.SPRING:
        return 'platform-spring';
      case PlatformType.BREAKING:
        return 'platform-breaking';
      case PlatformType.MOVING:
        return 'platform-moving';
      case PlatformType.DISAPPEARING:
        return 'platform-disappearing';
      case PlatformType.NORMAL:
      default:
        return 'platform-normal';
    }
  }

  private setupPhysics(): void {
    if (!this.body) return;

    const body = this.body as Phaser.Physics.Arcade.Body;
    
    // Set physics body size
    body.setSize(this.width, 16);
    
    // Configure physics properties
    body.setImmovable(true); // Platforms don't move from collisions
    body.setCollideWorldBounds(false); // Allow platforms to move off-screen
    
    // Platform-specific collision setup
    if (this.type === PlatformType.MOVING) {
      body.setImmovable(false); // Moving platforms can be affected by physics
    }

    console.log(`Platform physics configured for type: ${this.type}`);
  }

  private configurePlatformType(): void {
    switch (this.type) {
      case PlatformType.NORMAL:
        this.bounceMultiplier = 1.0;
        break;
        
      case PlatformType.SPRING:
        this.bounceMultiplier = 1.5; // 50% stronger bounce
        this.setTint(0xFF6B6B); // Light red tint
        break;
        
      case PlatformType.MOVING:
        this.isMoving = true;
        this.movementSpeed = 50; // pixels per second
        this.movementRange = 100; // pixels
        this.setTint(0x4ECDC4); // Teal tint
        break;
        
      case PlatformType.BREAKING:
        this.setTint(0xFFE66D); // Yellow tint to indicate fragility
        break;
        
      case PlatformType.DISAPPEARING:
        this.setTint(0xA8E6CF); // Light green tint
        break;
    }
  }

  public update(delta: number): void {
    if (!this.isActive) return;

    const deltaSeconds = delta / 1000;
    
    // Handle moving platform logic
    if (this.isMoving && this.movementSpeed && this.movementRange) {
      this.updateMovement(deltaSeconds);
    }

    // Handle disappearing platform logic
    if (this.type === PlatformType.DISAPPEARING) {
      this.updateDisappearing(deltaSeconds);
    }
  }

  private updateMovement(deltaSeconds: number): void {
    if (!this.movementSpeed || !this.movementRange) return;

    // Move platform back and forth
    const movement = this.movementSpeed * deltaSeconds * this.movementDirection;
    this.x += movement;

    // Check if we've reached movement bounds
    const distanceFromInitial = Math.abs(this.x - this.initialX);
    if (distanceFromInitial >= this.movementRange / 2) {
      this.movementDirection *= -1; // Reverse direction
      
      // Clamp position to prevent overshooting
      if (this.x > this.initialX + this.movementRange / 2) {
        this.x = this.initialX + this.movementRange / 2;
      } else if (this.x < this.initialX - this.movementRange / 2) {
        this.x = this.initialX - this.movementRange / 2;
      }
    }
  }

  private updateDisappearing(deltaSeconds: number): void {
    // Disappearing platforms flash before vanishing
    this.movementTimer += deltaSeconds;
    
    if (this.movementTimer > 3.0) { // Start flashing after 3 seconds
      const flashSpeed = 5; // Flash frequency
      const alpha = 0.5 + 0.5 * Math.sin(this.movementTimer * flashSpeed);
      this.setAlpha(alpha);
      
      if (this.movementTimer > 5.0) { // Disappear after 5 seconds
        this.deactivate();
      }
    }
  }

  public onPlayerCollision(player: any): boolean {
    if (!this.isActive) return false;

    // Handle platform-specific collision effects
    switch (this.type) {
      case PlatformType.BREAKING:
        this.triggerBreaking();
        return true; // Allow bounce before breaking
        
      case PlatformType.DISAPPEARING:
        this.triggerDisappearing();
        return true; // Allow bounce before disappearing
        
      case PlatformType.SPRING:
      case PlatformType.MOVING:
      case PlatformType.NORMAL:
      default:
        return true; // Normal bounce
    }
  }

  private triggerBreaking(): void {
    console.log('Platform breaking triggered');
    
    // Add breaking animation/effect here
    this.setTint(0xFF4444); // Red tint when breaking
    
    // Schedule platform destruction
    this.scene.time.delayedCall(200, () => {
      this.deactivate();
    });
  }

  private triggerDisappearing(): void {
    console.log('Platform disappearing triggered');
    
    // Start immediate disappearing sequence
    this.movementTimer = 3.0; // Skip to flashing phase
  }

  public getBounceMultiplier(): number {
    return this.bounceMultiplier;
  }

  public isActiveState(): boolean {
    return this.isActive;
  }

  public deactivate(): void {
    this.isActive = false;
    this.setVisible(false);
    
    if (this.body) {
      (this.body as Phaser.Physics.Arcade.Body).enable = false;
    }
    
    console.log(`Platform deactivated: ${this.id}`);
  }

  public reset(x?: number, y?: number, type?: PlatformType): void {
    // Reset platform for object pooling
    this.isActive = true;
    this.setVisible(true);
    this.setAlpha(1);
    this.clearTint();
    this.movementTimer = 0;
    this.movementDirection = 1;
    
    if (x !== undefined && y !== undefined) {
      this.setPosition(x, y);
      this.initialX = x;
    }
    
    if (type !== undefined) {
      this.type = type;
      this.setTexture(Platform.getTextureForType(type));
      this.configurePlatformType();
    }
    
    if (this.body) {
      (this.body as Phaser.Physics.Arcade.Body).enable = true;
    }
    
    console.log(`Platform reset: ${this.type} at (${this.x}, ${this.y})`);
  }

  public getCollisionBounds(): { width: number; height: number; x: number; y: number } {
    return {
      width: this.width,
      height: 16,
      x: this.x - this.width / 2,
      y: this.y - 8
    };
  }

  // x and y properties are inherited from Phaser.Physics.Arcade.Sprite

  public destroy(): void {
    console.log(`Platform destroyed: ${this.id}`);
    super.destroy();
  }
}