import { GameState, PlayerState, PlatformType } from '../../types/game';
import { GAME_CONSTANTS } from '../config/GameConfig';
import { Player } from '../entities/Player';
import { Platform } from '../entities/Platform';
import { PhysicsSystem } from '../systems/PhysicsSystem';
import { InputSystem } from '../systems/InputSystem';
import { PlatformGenerator } from '../systems/PlatformGenerator';
import { GameStateManager } from '../managers/GameStateManager';

export class GameScene extends Phaser.Scene {
  private gameState: GameState;
  private player: Player;
  private platforms: Phaser.GameObjects.Group;
  private gameStateManager: GameStateManager;
  private physicsSystem: PhysicsSystem;
  private inputSystem: InputSystem;
  private platformGenerator: PlatformGenerator;

  // WeChat Performance monitoring
  private fpsText: Phaser.GameObjects.Text;
  private lastTime: number = 0;
  private frameCount: number = 0;
  private fps: number = 60;
  private memoryUsage: number = 0;
  private performanceWarnings: number = 0;
  private lastGCTime: number = 0;
  private canvasCleanupCounter: number = 0;

  // Camera tracking
  private cameraTarget: number = 0;
  private highestPlayerY: number = 0;

  // Object pooling
  private platformPool: Platform[] = [];
  private activeComponents: Set<any> = new Set();

  constructor() {
    super({ key: 'GameScene' });
  }

  preload(): void {
    console.log('GameScene: Starting preload phase');
    
    // Create simple colored rectangles as placeholders for sprites
    // In production, these would be loaded from actual sprite files
    this.createPlaceholderAssets();
    
    // Update loading progress
    if (window.updateLoadingProgress) {
      window.updateLoadingProgress(50, '加载游戏资源中...');
    }
  }

  create(): void {
    console.log('GameScene: Starting create phase');

    try {
      // Initialize game state
      this.initializeGameState();

      // Setup physics world
      this.setupPhysicsWorld();

      // Create game systems
      this.createGameSystems();

      // Create game entities
      this.createGameEntities();

      // Setup camera
      this.setupCamera();

      // Create UI elements
      this.createUI();

      // Setup input handling
      this.setupInput();

      // Start game loop
      this.startGameLoop();

      console.log('GameScene: Created successfully');
      
      // Hide loading screen
      if (window.hideLoading) {
        window.hideLoading();
      }

    } catch (error) {
      console.error('GameScene: Error during creation:', error);
      if (window.showError) {
        window.showError('游戏初始化失败，请重新加载。');
      }
    }
  }

  update(time: number, delta: number): void {
    if (!this.gameState.isPlaying || this.gameState.isPaused) {
      return;
    }

    try {
      // Cap delta time for WeChat stability
      const cappedDelta = Math.min(delta, GAME_CONSTANTS.PERFORMANCE.MAX_DELTA_TIME);
      
      // Update performance monitoring
      this.updatePerformanceMonitoring(time, cappedDelta);
      
      // WeChat memory management
      this.updateMemoryManagement(time);

      // Update game systems with capped delta
      this.physicsSystem.update(cappedDelta);
      this.inputSystem.update(cappedDelta);
      this.updatePlatformGeneration();

      // Update game entities
      this.player.update(cappedDelta);
      this.updatePlatforms(cappedDelta);

      // Update camera
      this.updateCamera();

      // Check game conditions
      this.checkGameConditions();

      // WeChat canvas cleanup
      this.updateCanvasCleanup();

      // Update game state
      this.gameState.gameTime += cappedDelta;

    } catch (error) {
      console.error('GameScene: Error during update:', error);
      this.handleGameError(error);
    }
  }

  private createPlaceholderAssets(): void {
    // Create colored rectangles as placeholder sprites
    const graphics = this.add.graphics();
    
    // Player sprite (green square)
    graphics.fillStyle(0x00FF00);
    graphics.fillRect(0, 0, GAME_CONSTANTS.PLAYER.SIZE.width, GAME_CONSTANTS.PLAYER.SIZE.height);
    graphics.generateTexture('player', GAME_CONSTANTS.PLAYER.SIZE.width, GAME_CONSTANTS.PLAYER.SIZE.height);
    
    // Normal platform (brown rectangle)
    graphics.clear();
    graphics.fillStyle(0x8B4513);
    graphics.fillRect(0, 0, GAME_CONSTANTS.PLATFORMS.BASE_WIDTH, 16);
    graphics.generateTexture('platform-normal', GAME_CONSTANTS.PLATFORMS.BASE_WIDTH, 16);
    
    // Spring platform (red rectangle)
    graphics.clear();
    graphics.fillStyle(0xFF6B6B);
    graphics.fillRect(0, 0, GAME_CONSTANTS.PLATFORMS.BASE_WIDTH, 16);
    graphics.generateTexture('platform-spring', GAME_CONSTANTS.PLATFORMS.BASE_WIDTH, 16);
    
    // Moving platform (teal rectangle)
    graphics.clear();
    graphics.fillStyle(0x4ECDC4);
    graphics.fillRect(0, 0, GAME_CONSTANTS.PLATFORMS.BASE_WIDTH, 16);
    graphics.generateTexture('platform-moving', GAME_CONSTANTS.PLATFORMS.BASE_WIDTH, 16);
    
    // Breaking platform (yellow rectangle)
    graphics.clear();
    graphics.fillStyle(0xFFE66D);
    graphics.fillRect(0, 0, GAME_CONSTANTS.PLATFORMS.BASE_WIDTH, 16);
    graphics.generateTexture('platform-breaking', GAME_CONSTANTS.PLATFORMS.BASE_WIDTH, 16);
    
    // Disappearing platform (light green rectangle)
    graphics.clear();
    graphics.fillStyle(0xA8E6CF);
    graphics.fillRect(0, 0, GAME_CONSTANTS.PLATFORMS.BASE_WIDTH, 16);
    graphics.generateTexture('platform-disappearing', GAME_CONSTANTS.PLATFORMS.BASE_WIDTH, 16);
    
    graphics.destroy();
  }

  private initializeGameState(): void {
    this.gameState = {
      isPlaying: true,
      isPaused: false,
      score: 0,
      platforms: [],
      player: null as any, // Will be set when player is created
      powerUps: [],
      camera: {
        x: 0,
        y: 0,
        zoom: 1
      },
      gameTime: 0
    };

    this.highestPlayerY = GAME_CONSTANTS.WORLD.HEIGHT - 100;
  }

  private setupPhysicsWorld(): void {
    // Configure physics world bounds
    const bounds = this.physics.world.bounds;
    bounds.setTo(
      -GAME_CONSTANTS.WORLD.BOUNDS_PADDING,
      -10000, // Allow upward movement
      GAME_CONSTANTS.WORLD.WIDTH + (GAME_CONSTANTS.WORLD.BOUNDS_PADDING * 2),
      GAME_CONSTANTS.WORLD.HEIGHT + 1000 // Allow falling below screen
    );

    // Configure world physics
    this.physics.world.setBoundsCollision(true, true, false, false); // Left, right, top, bottom
    this.physics.world.gravity.y = GAME_CONSTANTS.PHYSICS.GRAVITY;

    console.log('Physics world configured with gravity:', GAME_CONSTANTS.PHYSICS.GRAVITY);
  }

  private createGameSystems(): void {
    // Initialize game systems
    this.gameStateManager = new GameStateManager(this.gameState);
    this.physicsSystem = new PhysicsSystem(this.physics);
    this.inputSystem = new InputSystem(this.input);
    this.platformGenerator = new PlatformGenerator(this.physics, this.platformPool);

    // Add systems to active components for cleanup
    this.activeComponents.add(this.gameStateManager);
    this.activeComponents.add(this.physicsSystem);
    this.activeComponents.add(this.inputSystem);
    this.activeComponents.add(this.platformGenerator);
  }

  private createGameEntities(): void {
    // Create platforms group for collision detection
    this.platforms = this.add.group({
      runChildUpdate: true
    });

    // Create initial player
    const startX = GAME_CONSTANTS.WORLD.WIDTH / 2;
    const startY = GAME_CONSTANTS.WORLD.HEIGHT - 100;
    
    this.player = new Player(this, startX, startY);
    this.gameState.player = this.player;

    // Generate initial platforms
    this.generateInitialPlatforms();

    // Setup collision detection between player and platforms
    this.setupCollisions();

    console.log('Game entities created - Player and platforms initialized');
  }

  private generateInitialPlatforms(): void {
    // Create starting platform
    const startPlatform = new Platform(
      this,
      GAME_CONSTANTS.WORLD.WIDTH / 2 - 40,
      GAME_CONSTANTS.WORLD.HEIGHT - 50,
      PlatformType.NORMAL
    );
    
    this.platforms.add(startPlatform);
    this.gameState.platforms.push(startPlatform);

    // Generate initial platform ladder
    this.platformGenerator.generateInitialPlatforms(this.platforms, this.gameState.platforms);
  }

  private setupCollisions(): void {
    // Setup collision between player and platforms
    this.physics.add.collider(
      this.player,
      this.platforms,
      this.handlePlayerPlatformCollision,
      this.checkPlayerPlatformCollision,
      this
    );

    console.log('Collision detection configured between player and platforms');
  }

  private checkPlayerPlatformCollision(player: any, platform: any): boolean {
    // Only collide when player is falling down onto the platform
    // This prevents collision when jumping through platforms from below
    return player.body.velocity.y > 0 && 
           player.y < platform.y && 
           platform.isActiveState();
  }

  private handlePlayerPlatformCollision(player: any, platform: any): void {
    // Only handle collision if platform is active
    if (!platform.isActiveState()) {
      return;
    }

    // Let the platform handle its specific collision logic
    const shouldBounce = platform.onPlayerCollision(player);
    
    if (shouldBounce) {
      // Calculate bounce velocity based on platform type
      const bounceMultiplier = platform.getBounceMultiplier ? platform.getBounceMultiplier() : 1.0;
      const bounceVelocity = GAME_CONSTANTS.PHYSICS.JUMP_VELOCITY * bounceMultiplier;
      
      // Trigger platform jump with appropriate velocity
      player.platformJump(bounceVelocity);
      
      console.log(`Player bounced off ${platform.type} platform with multiplier ${bounceMultiplier}`);
    }
  }

  private setupCamera(): void {
    const camera = this.cameras.main;
    
    // Configure camera bounds (infinite upward scrolling)
    camera.setBounds(
      0, 
      -10000, 
      GAME_CONSTANTS.WORLD.WIDTH, 
      GAME_CONSTANTS.WORLD.HEIGHT + 10000
    );
    
    // Set camera to follow player with smooth interpolation
    camera.startFollow(this.player, true, GAME_CONSTANTS.CAMERA.SMOOTHING, GAME_CONSTANTS.CAMERA.SMOOTHING);
    camera.setFollowOffset(0, GAME_CONSTANTS.CAMERA.OFFSET_Y);
    
    // Set camera deadzone for smooth following
    camera.setDeadzone(100, 100);

    console.log('Camera configured for player following');
  }

  private createUI(): void {
    // Create performance monitoring display (development only)
    if (process.env.NODE_ENV === 'development') {
      this.fpsText = this.add.text(10, 10, 'FPS: 60', {
        fontFamily: 'Arial',
        fontSize: '16px',
        color: '#ffffff',
        stroke: '#000000',
        strokeThickness: 2
      });
      this.fpsText.setScrollFactor(0); // Fixed position relative to camera
    }

    // Create score display
    const scoreText = this.add.text(GAME_CONSTANTS.WORLD.WIDTH - 10, 10, 'Score: 0', {
      fontFamily: 'Arial',
      fontSize: '20px',
      color: '#ffffff',
      stroke: '#000000',
      strokeThickness: 2
    });
    scoreText.setOrigin(1, 0);
    scoreText.setScrollFactor(0);
  }

  private setupInput(): void {
    // Configure input system with player reference
    this.inputSystem.setPlayer(this.player);
    
    // Enable input event handling
    this.inputSystem.enable();

    console.log('Input system configured and enabled');
  }

  private startGameLoop(): void {
    // Initialize game loop timing
    this.lastTime = this.time.now;
    
    // Set up periodic garbage collection (performance optimization)
    this.time.addEvent({
      delay: GAME_CONSTANTS.PERFORMANCE.GARBAGE_COLLECT_INTERVAL,
      callback: this.performGarbageCollection,
      callbackScope: this,
      loop: true
    });

    console.log('Game loop started successfully');
  }

  private updatePerformanceMonitoring(time: number, delta: number): void {
    this.frameCount++;
    
    if (time - this.lastTime >= GAME_CONSTANTS.PERFORMANCE.PERFORMANCE_MONITOR_INTERVAL) {
      this.fps = Math.round((this.frameCount * 1000) / (time - this.lastTime));
      
      if (this.fpsText) {
        this.fpsText.setText(`FPS: ${this.fps} | Mem: ${Math.round(this.memoryUsage / 1024 / 1024)}MB`);
      }
      
      this.frameCount = 0;
      this.lastTime = time;

      // WeChat performance warnings
      if (this.fps < GAME_CONSTANTS.PERFORMANCE.MIN_FPS) {
        this.performanceWarnings++;
        console.warn(`WeChat Performance warning: FPS dropped to ${this.fps} (Warning #${this.performanceWarnings})`);
        
        // Trigger performance optimization after multiple warnings
        if (this.performanceWarnings >= 3) {
          this.optimizeForLowPerformance();
        }
      } else {
        // Reset warnings if performance recovers
        this.performanceWarnings = Math.max(0, this.performanceWarnings - 1);
      }
    }
  }

  private updateMemoryManagement(time: number): void {
    // Estimate memory usage for WeChat monitoring
    const platformCount = this.platforms.children.size;
    const activeObjects = this.activeComponents.size;
    this.memoryUsage = (platformCount * 1024) + (activeObjects * 512); // Rough estimation
    
    // Check memory threshold
    if (this.memoryUsage > GAME_CONSTANTS.PERFORMANCE.MEMORY_WARNING_THRESHOLD) {
      console.warn(`WeChat Memory warning: ${Math.round(this.memoryUsage / 1024 / 1024)}MB used`);
      this.performAggressiveCleanup();
    }
    
    // Periodic garbage collection for WeChat
    if (time - this.lastGCTime > GAME_CONSTANTS.PERFORMANCE.GARBAGE_COLLECT_INTERVAL) {
      this.performGarbageCollection();
      this.lastGCTime = time;
    }
  }

  private updateCanvasCleanup(): void {
    this.canvasCleanupCounter++;
    
    // Periodic canvas cleanup for WeChat performance
    if (this.canvasCleanupCounter >= GAME_CONSTANTS.PERFORMANCE.CANVAS_CLEAR_FREQUENCY) {
      this.cleanupCanvasMemory();
      this.canvasCleanupCounter = 0;
    }
  }

  private optimizeForLowPerformance(): void {
    console.log('WeChat: Activating low performance mode');
    
    // Reduce physics quality
    this.physics.world.timeScale = 0.8;
    
    // Reduce platform generation frequency
    this.platformGenerator.setDifficulty(0.5);
    
    // Clear unnecessary visual effects
    this.clearVisualEffects();
    
    // Reset performance warnings
    this.performanceWarnings = 0;
  }

  private performAggressiveCleanup(): void {
    console.log('WeChat: Performing aggressive memory cleanup');
    
    // Clean platforms more aggressively
    const playerY = this.player.y;
    const aggressiveThreshold = playerY + GAME_CONSTANTS.WORLD.HEIGHT * 0.7;
    
    this.platforms.children.entries.forEach((platform: any) => {
      if (platform && platform.y > aggressiveThreshold) {
        this.returnPlatformToPool(platform as Platform);
        this.platforms.remove(platform);
        platform.destroy();
      }
    });
    
    // Clear texture cache if needed
    this.textures.removeTextureSource('__MISSING');
  }

  private cleanupCanvasMemory(): void {
    // WeChat canvas memory cleanup
    const renderer = this.sys.game.renderer as Phaser.Renderer.Canvas.CanvasRenderer;
    if (renderer && renderer.gameCanvas) {
      const ctx = renderer.gameCanvas.getContext('2d');
      if (ctx) {
        // Force canvas to release memory
        ctx.clearRect(0, 0, renderer.gameCanvas.width, renderer.gameCanvas.height);
      }
    }
  }

  private clearVisualEffects(): void {
    // Remove or reduce visual effects for performance
    this.children.list.forEach(child => {
      if (child instanceof Phaser.GameObjects.Particles.ParticleEmitter) {
        child.setActive(false);
        child.setVisible(false);
      }
    });
  }

  private updatePlatformGeneration(): void {
    // Generate new platforms as player climbs higher
    const generateThreshold = this.platformGenerator.getLastPlatformY() + (GAME_CONSTANTS.WORLD.HEIGHT * 1.5);
    
    if (this.player.y < generateThreshold) {
      // Generate platforms ahead of the player
      const newPlatforms = this.platformGenerator.generatePlatformsInRange(
        this.platformGenerator.getLastPlatformY(),
        this.platformGenerator.getLastPlatformY() - (GAME_CONSTANTS.PLATFORMS.MAX_GAP * 5),
        this.platforms
      );
      
      // Add new platforms to game state
      newPlatforms.forEach(platform => {
        this.gameState.platforms.push(platform);
      });
    }
  }

  private updatePlatforms(delta: number): void {
    // Update all active platforms
    this.platforms.children.entries.forEach((platform: any) => {
      if (platform && platform.update) {
        platform.update(delta);
      }
    });

    // Clean up platforms that are too far below the player
    this.cleanupPlatforms();
  }

  private cleanupPlatforms(): void {
    const playerY = this.player.y;
    const cleanupThreshold = playerY + GAME_CONSTANTS.WORLD.HEIGHT;

    this.platforms.children.entries.forEach((platform: any) => {
      if (platform && platform.y > cleanupThreshold) {
        // Remove from gameState array
        const index = this.gameState.platforms.indexOf(platform);
        if (index > -1) {
          this.gameState.platforms.splice(index, 1);
        }
        
        // Return to pool
        this.returnPlatformToPool(platform as Platform);
        
        // Remove from group
        this.platforms.remove(platform);
        platform.destroy();
      }
    });
  }

  private returnPlatformToPool(platform: Platform): void {
    if (this.platformPool.length < GAME_CONSTANTS.PERFORMANCE.OBJECT_POOL_SIZE) {
      platform.reset();
      this.platformPool.push(platform);
    }
  }

  private updateCamera(): void {
    // Track highest player position for camera following
    if (this.player.y < this.highestPlayerY) {
      this.highestPlayerY = this.player.y;
      this.cameraTarget = this.highestPlayerY;
    }

    // Ensure camera doesn't move down once player has gone up
    const camera = this.cameras.main;
    if (camera.scrollY > this.cameraTarget - GAME_CONSTANTS.CAMERA.OFFSET_Y) {
      camera.scrollY = this.cameraTarget - GAME_CONSTANTS.CAMERA.OFFSET_Y;
    }
  }

  private checkGameConditions(): void {
    // Check if player has fallen below the visible area (game over condition)
    const camera = this.cameras.main;
    const gameOverY = camera.scrollY + GAME_CONSTANTS.WORLD.HEIGHT + 100;
    
    if (this.player.y > gameOverY) {
      this.triggerGameOver();
    }

    // Update score based on height
    const newScore = Math.max(0, Math.floor((this.highestPlayerY - (GAME_CONSTANTS.WORLD.HEIGHT - 100)) / -10));
    if (newScore > this.gameState.score) {
      this.gameState.score = newScore;
      // Update score display would go here
    }
  }

  private triggerGameOver(): void {
    console.log('Game Over triggered - Player fell below screen');
    
    this.gameState.isPlaying = false;
    this.player.setState(PlayerState.DEAD);
    
    // Stop physics
    this.physics.world.pause();
    
    // TODO: Show game over screen
    // This will be implemented in Task 5
    console.log(`Final Score: ${this.gameState.score}`);
  }

  private performGarbageCollection(): void {
    // Force garbage collection for memory management
    if (window.gc) {
      window.gc();
    }
    
    console.log(`Performance: ${this.platforms.children.size} platforms active, Score: ${this.gameState.score}`);
  }

  private handleGameError(error: any): void {
    console.error('GameScene: Runtime error:', error);
    
    // Pause game to prevent further errors
    this.gameState.isPaused = true;
    this.physics.world.pause();
    
    if (window.showError) {
      window.showError('游戏运行时发生错误，请重新加载页面。');
    }
  }

  // Public methods for external control
  public pauseGame(): void {
    this.gameState.isPaused = true;
    this.physics.world.pause();
    this.inputSystem.disable();
  }

  public resumeGame(): void {
    this.gameState.isPaused = false;
    this.physics.world.resume();
    this.inputSystem.enable();
  }

  public restartGame(): void {
    console.log('Restarting game...');
    this.scene.restart();
  }

  public getGameState(): GameState {
    return this.gameState;
  }

  // Cleanup on scene shutdown
  destroy(): void {
    console.log('GameScene: Cleaning up...');
    
    // Cleanup all active components
    this.activeComponents.forEach(component => {
      if (component && component.destroy) {
        component.destroy();
      }
    });
    this.activeComponents.clear();

    // Clear object pools
    this.platformPool.length = 0;

    super.destroy();
  }
}