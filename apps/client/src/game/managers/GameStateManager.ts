import { GameState } from '../../types/game';

export class GameStateManager {
  private gameState: GameState;

  constructor(gameState: GameState) {
    this.gameState = gameState;
    console.log('GameStateManager initialized');
  }

  public getState(): GameState {
    return this.gameState;
  }

  public setState(newState: Partial<GameState>): void {
    Object.assign(this.gameState, newState);
  }

  public pauseGame(): void {
    this.gameState.isPaused = true;
    console.log('Game paused');
  }

  public resumeGame(): void {
    this.gameState.isPaused = false;
    console.log('Game resumed');
  }

  public startGame(): void {
    this.gameState.isPlaying = true;
    this.gameState.isPaused = false;
    console.log('Game started');
  }

  public stopGame(): void {
    this.gameState.isPlaying = false;
    console.log('Game stopped');
  }

  public resetGame(): void {
    this.gameState.score = 0;
    this.gameState.gameTime = 0;
    this.gameState.isPlaying = false;
    this.gameState.isPaused = false;
    this.gameState.platforms = [];
    this.gameState.powerUps = [];
    console.log('Game state reset');
  }

  public destroy(): void {
    console.log('GameStateManager destroyed');
  }
}