import { GAME_CONSTANTS } from '../config/GameConfig';

/**
 * WeChat Lifecycle Manager
 * Manages WeChat-specific lifecycle events, memory management, and performance optimization
 */
export class WeChatLifecycleManager {
  private scene: Phaser.Scene;
  private isPaused: boolean = false;
  private resumeTimer?: Phaser.Time.TimerEvent;
  private memoryWarnings: number = 0;
  private lastMemoryCheck: number = 0;
  private performanceMode: 'normal' | 'optimized' | 'minimal' = 'normal';

  // WeChat API references
  private wx: any;

  constructor(scene: Phaser.Scene) {
    this.scene = scene;
    this.wx = (window as any).wx;
    
    this.initializeWeChatEvents();
    this.initializeMemoryMonitoring();
    
    console.log('WeChatLifecycleManager initialized');
  }

  private initializeWeChatEvents(): void {
    if (!this.wx) {
      console.warn('WeChat API not available, using fallback lifecycle management');
      this.initializeFallbackEvents();
      return;
    }

    // WeChat app lifecycle events
    this.wx.onShow(() => {
      console.log('WeChat: App resumed');
      this.handleAppResume();
    });

    this.wx.onHide(() => {
      console.log('WeChat: App paused');
      this.handleAppPause();
    });

    // WeChat memory warnings
    this.wx.onMemoryWarning((res: any) => {
      console.warn('WeChat Memory Warning:', res.level);
      this.handleMemoryWarning(res.level);
    });

    // WeChat audio interruption
    this.wx.onAudioInterruptionBegin(() => {
      console.log('WeChat: Audio interruption began');
      this.handleAudioInterruption();
    });

    this.wx.onAudioInterruptionEnd(() => {
      console.log('WeChat: Audio interruption ended');
      this.handleAudioResume();
    });

    // WeChat network status
    this.wx.onNetworkStatusChange((res: any) => {
      console.log('WeChat: Network status changed', res);
      this.handleNetworkChange(res);
    });
  }

  private initializeFallbackEvents(): void {
    // Fallback for non-WeChat environments
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.handleAppPause();
      } else {
        this.handleAppResume();
      }
    });

    window.addEventListener('focus', () => {
      this.handleAppResume();
    });

    window.addEventListener('blur', () => {
      this.handleAppPause();
    });
  }

  private initializeMemoryMonitoring(): void {
    // Set up periodic memory monitoring
    this.scene.time.addEvent({
      delay: 5000, // Check every 5 seconds
      callback: this.checkMemoryUsage,
      callbackScope: this,
      loop: true
    });
  }

  private handleAppPause(): void {
    if (this.isPaused) return;
    
    this.isPaused = true;
    
    // Pause game systems
    if (this.scene.scene.isActive()) {
      this.scene.scene.pause();
    }
    
    // Pause physics
    this.scene.physics.world.pause();
    
    // Pause audio
    if (this.scene.sound) {
      this.scene.sound.pauseAll();
    }
    
    // Clear any running timers
    if (this.resumeTimer) {
      this.resumeTimer.destroy();
      this.resumeTimer = undefined;
    }
    
    // Trigger memory cleanup
    this.performMemoryCleanup();
    
    console.log('WeChat: Game paused successfully');
  }

  private handleAppResume(): void {
    if (!this.isPaused) return;
    
    // Add resume delay for WeChat stability
    if (this.resumeTimer) {
      this.resumeTimer.destroy();
    }
    
    this.resumeTimer = this.scene.time.delayedCall(GAME_CONSTANTS.WECHAT.RESUME_DELAY, () => {
      this.isPaused = false;
      
      // Resume game systems
      if (this.scene.scene.isPaused()) {
        this.scene.scene.resume();
      }
      
      // Resume physics
      this.scene.physics.world.resume();
      
      // Resume audio (if not iOS WeChat)
      if (this.scene.sound && !GAME_CONSTANTS.WECHAT.PRELOAD_AUDIO_IOS) {
        this.scene.sound.resumeAll();
      }
      
      console.log('WeChat: Game resumed successfully');
    });
  }

  private handleMemoryWarning(level: number): void {
    this.memoryWarnings++;
    console.warn(`WeChat Memory Warning Level ${level} (Warning #${this.memoryWarnings})`);
    
    switch (level) {
      case 1: // Trim
        this.performMemoryCleanup();
        break;
      case 2: // Moderate
        this.performAggressiveCleanup();
        this.setPerformanceMode('optimized');
        break;
      case 3: // Critical
        this.performCriticalCleanup();
        this.setPerformanceMode('minimal');
        break;
    }
  }

  private handleAudioInterruption(): void {
    if (this.scene.sound) {
      this.scene.sound.pauseAll();
    }
  }

  private handleAudioResume(): void {
    if (this.scene.sound && !this.isPaused && !GAME_CONSTANTS.WECHAT.PRELOAD_AUDIO_IOS) {
      this.scene.sound.resumeAll();
    }
  }

  private handleNetworkChange(status: any): void {
    // Handle network status changes
    if (!status.isConnected) {
      console.warn('WeChat: Network disconnected');
      // Switch to offline mode or pause network-dependent features
    } else {
      console.log('WeChat: Network reconnected', status.networkType);
    }
  }

  private checkMemoryUsage(): void {
    const currentTime = this.scene.time.now;
    this.lastMemoryCheck = currentTime;
    
    // Estimate memory usage
    const sceneChildren = this.scene.children.list.length;
    const textureCache = Object.keys(this.scene.textures.list).length;
    const physicsObjects = this.scene.physics.world.bodies.entries.length;
    
    const estimatedMemory = (sceneChildren * 1024) + (textureCache * 2048) + (physicsObjects * 512);
    
    if (estimatedMemory > GAME_CONSTANTS.WECHAT.MEMORY_LIMIT * 0.8) {
      console.warn(`WeChat: High memory usage estimated: ${Math.round(estimatedMemory / 1024 / 1024)}MB`);
      this.performMemoryCleanup();
    }
  }

  private performMemoryCleanup(): void {
    console.log('WeChat: Performing memory cleanup');
    
    // Clear unused textures
    const textureManager = this.scene.textures;
    Object.keys((textureManager as any).list).forEach(key => {
      const texture = (textureManager as any).list[key];
      if (texture && !texture.hasFrames && key !== '__DEFAULT' && key !== '__MISSING') {
        textureManager.remove(key);
      }
    });
    
    // Force garbage collection if available
    if ((window as any).gc) {
      (window as any).gc();
    }
    
    // Clear console logs to free memory
    console.clear();
  }

  private performAggressiveCleanup(): void {
    console.log('WeChat: Performing aggressive cleanup');
    
    // Perform basic cleanup
    this.performMemoryCleanup();
    
    // Remove inactive game objects
    this.scene.children.list.forEach(child => {
      if (!child.active || ('visible' in child && !(child as any).visible)) {
        child.destroy();
      }
    });
    
    // Clear sound cache
    if (this.scene.sound) {
      this.scene.sound.removeAll();
    }
    
    // Clear animation cache
    if (this.scene.anims) {
      // Clear all animations safely
      (this.scene.anims as any).anims?.clear();
    }
  }

  private performCriticalCleanup(): void {
    console.log('WeChat: Performing critical cleanup');
    
    // Perform aggressive cleanup
    this.performAggressiveCleanup();
    
    // Restart scene to free all resources
    this.scene.time.delayedCall(1000, () => {
      console.log('WeChat: Restarting scene due to critical memory pressure');
      this.scene.scene.restart();
    });
  }

  private setPerformanceMode(mode: 'normal' | 'optimized' | 'minimal'): void {
    if (this.performanceMode === mode) return;
    
    this.performanceMode = mode;
    console.log(`WeChat: Performance mode set to ${mode}`);
    
    switch (mode) {
      case 'optimized':
        // Reduce physics quality
        this.scene.physics.world.timeScale = 0.8;
        // Reduce rendering quality
        if (this.scene.renderer) {
          (this.scene.renderer as any).antialias = false;
        }
        break;
        
      case 'minimal':
        // Minimal physics
        this.scene.physics.world.timeScale = 0.6;
        // Disable visual effects
        this.scene.children.list.forEach(child => {
          if (child instanceof Phaser.GameObjects.Particles.ParticleEmitter) {
            child.setActive(false);
            child.setVisible(false);
          }
        });
        break;
        
      default: // normal
        this.scene.physics.world.timeScale = 1.0;
        break;
    }
  }

  public isPausedState(): boolean {
    return this.isPaused;
  }

  public getPerformanceMode(): string {
    return this.performanceMode;
  }

  public getMemoryWarnings(): number {
    return this.memoryWarnings;
  }

  public triggerManualCleanup(): void {
    this.performMemoryCleanup();
  }

  public destroy(): void {
    if (this.resumeTimer) {
      this.resumeTimer.destroy();
      this.resumeTimer = undefined;
    }
    
    console.log('WeChatLifecycleManager destroyed');
  }
}