import { GAME_CONSTANTS } from '../config/GameConfig';

/**
 * WeChat Input Manager
 * Optimized touch input handling for WeChat WebView environment
 */
export class WeChatInputManager {
  private scene: Phaser.Scene;
  private player: any; // Player reference
  private isEnabled: boolean = true;
  
  // Touch tracking
  private touchStartX: number = 0;
  private touchStartTime: number = 0;
  private lastTouchX: number = 0;
  private touchSensitivity: number = GAME_CONSTANTS.WECHAT.TOUCH_SENSITIVITY;
  
  // Input filtering for WeChat
  private inputBuffer: number[] = [];
  private bufferSize: number = 3;
  private lastInputTime: number = 0;
  private inputThrottleDelay: number = 16; // ~60fps throttling
  
  // Gesture detection
  private tapThreshold: number = 10; // pixels
  private tapTimeThreshold: number = 200; // milliseconds
  private swipeThreshold: number = 50; // pixels
  
  constructor(scene: Phaser.Scene) {
    this.scene = scene;
    this.initializeWeChatInput();
    
    console.log('WeChatInputManager initialized');
  }

  private initializeWeChatInput(): void {
    // WeChat-optimized touch events
    this.scene.input.on('pointerdown', this.handleTouchStart, this);
    this.scene.input.on('pointermove', this.handleTouchMove, this);
    this.scene.input.on('pointerup', this.handleTouchEnd, this);
    
    // Handle input on the canvas directly for WeChat compatibility
    const canvas = this.scene.sys.game.canvas;
    if (canvas) {
      canvas.addEventListener('touchstart', this.handleNativeTouchStart.bind(this), { passive: false });
      canvas.addEventListener('touchmove', this.handleNativeTouchMove.bind(this), { passive: false });
      canvas.addEventListener('touchend', this.handleNativeTouchEnd.bind(this), { passive: false });
    }
    
    console.log('WeChat touch input events initialized');
  }

  private handleTouchStart(pointer: Phaser.Input.Pointer): void {
    if (!this.isEnabled) return;
    
    this.touchStartX = pointer.x;
    this.touchStartTime = this.scene.time.now;
    this.lastTouchX = pointer.x;
    
    // Immediate jump response for better feel
    if (this.player && this.player.jump) {
      this.player.jump();
    }
  }

  private handleTouchMove(pointer: Phaser.Input.Pointer): void {
    if (!this.isEnabled || !this.player) return;
    
    const currentTime = this.scene.time.now;
    
    // Throttle input updates for WeChat performance
    if (currentTime - this.lastInputTime < this.inputThrottleDelay) {
      return;
    }
    
    this.lastInputTime = currentTime;
    this.lastTouchX = pointer.x;
    
    // Calculate horizontal input based on touch position
    const screenCenter = GAME_CONSTANTS.WORLD.WIDTH / 2;
    const touchOffset = pointer.x - screenCenter;
    const normalizedInput = Math.max(-1, Math.min(1, touchOffset / (screenCenter * 0.7)));
    
    // Apply smoothing for WeChat touch responsiveness
    this.addToInputBuffer(normalizedInput);
    const smoothedInput = this.getSmoothedInput();
    
    // Apply to player
    if (this.player.setHorizontalInput) {
      this.player.setHorizontalInput(smoothedInput);
    }
  }

  private handleTouchEnd(pointer: Phaser.Input.Pointer): void {
    if (!this.isEnabled) return;
    
    const touchDuration = this.scene.time.now - this.touchStartTime;
    const touchDistance = Math.abs(pointer.x - this.touchStartX);
    
    // Detect tap vs swipe
    if (touchDuration < this.tapTimeThreshold && touchDistance < this.tapThreshold) {
      this.handleTap(pointer);
    } else if (touchDistance > this.swipeThreshold) {
      this.handleSwipe(pointer);
    }
    
    // Reset horizontal input
    if (this.player && this.player.setHorizontalInput) {
      this.player.setHorizontalInput(0);
    }
    
    // Clear input buffer
    this.inputBuffer = [];
  }

  private handleNativeTouchStart(event: TouchEvent): void {
    event.preventDefault(); // Prevent default WeChat behaviors
    
    if (event.touches.length > 0) {
      const touch = event.touches[0];
      const rect = (event.target as HTMLCanvasElement).getBoundingClientRect();
      
      // Convert to game coordinates
      const gameX = (touch.clientX - rect.left) * (GAME_CONSTANTS.WORLD.WIDTH / rect.width);
      const gameY = (touch.clientY - rect.top) * (GAME_CONSTANTS.WORLD.HEIGHT / rect.height);
      
      // Create pointer-like object for consistency
      const mockPointer = {
        x: gameX,
        y: gameY,
        isDown: true
      };
      
      // Process as normal touch start
      this.touchStartX = gameX;
      this.touchStartTime = this.scene.time.now;
      
      // Trigger jump
      if (this.player && this.player.jump) {
        this.player.jump();
      }
    }
  }

  private handleNativeTouchMove(event: TouchEvent): void {
    event.preventDefault();
    
    if (event.touches.length > 0) {
      const touch = event.touches[0];
      const rect = (event.target as HTMLCanvasElement).getBoundingClientRect();
      
      const gameX = (touch.clientX - rect.left) * (GAME_CONSTANTS.WORLD.WIDTH / rect.width);
      
      // Process horizontal movement
      const screenCenter = GAME_CONSTANTS.WORLD.WIDTH / 2;
      const touchOffset = gameX - screenCenter;
      const normalizedInput = Math.max(-1, Math.min(1, touchOffset / (screenCenter * 0.7)));
      
      this.addToInputBuffer(normalizedInput * this.touchSensitivity);
      const smoothedInput = this.getSmoothedInput();
      
      if (this.player && this.player.setHorizontalInput) {
        this.player.setHorizontalInput(smoothedInput);
      }
    }
  }

  private handleNativeTouchEnd(event: TouchEvent): void {
    event.preventDefault();
    
    // Reset horizontal input
    if (this.player && this.player.setHorizontalInput) {
      this.player.setHorizontalInput(0);
    }
    
    this.inputBuffer = [];
  }

  private handleTap(pointer: Phaser.Input.Pointer): void {
    console.log('WeChat: Tap detected');
    
    // Additional tap logic could go here
    // For example, activate power-ups or special abilities
  }

  private handleSwipe(pointer: Phaser.Input.Pointer): void {
    const swipeDirection = pointer.x > this.touchStartX ? 'right' : 'left';
    const swipeDistance = Math.abs(pointer.x - this.touchStartX);
    
    console.log(`WeChat: Swipe ${swipeDirection} detected, distance: ${swipeDistance}`);
    
    // Could be used for special moves or menu navigation
  }

  private addToInputBuffer(input: number): void {
    this.inputBuffer.push(input);
    
    // Keep buffer size limited
    if (this.inputBuffer.length > this.bufferSize) {
      this.inputBuffer.shift();
    }
  }

  private getSmoothedInput(): number {
    if (this.inputBuffer.length === 0) return 0;
    
    // Simple moving average for smoother input
    const sum = this.inputBuffer.reduce((acc, val) => acc + val, 0);
    return sum / this.inputBuffer.length;
  }

  public setPlayer(player: any): void {
    this.player = player;
    console.log('WeChat Input: Player reference set');
  }

  public enable(): void {
    this.isEnabled = true;
    console.log('WeChat Input: Enabled');
  }

  public disable(): void {
    this.isEnabled = false;
    
    // Clear any active input
    if (this.player && this.player.setHorizontalInput) {
      this.player.setHorizontalInput(0);
    }
    
    this.inputBuffer = [];
    console.log('WeChat Input: Disabled');
  }

  public setSensitivity(sensitivity: number): void {
    this.touchSensitivity = Math.max(0.1, Math.min(2.0, sensitivity));
    console.log(`WeChat Input: Sensitivity set to ${this.touchSensitivity}`);
  }

  public isInputEnabled(): boolean {
    return this.isEnabled;
  }

  public update(delta: number): void {
    // Update input system if needed
    // This could include input prediction or advanced filtering
  }

  public destroy(): void {
    // Clean up event listeners
    this.scene.input.off('pointerdown', this.handleTouchStart, this);
    this.scene.input.off('pointermove', this.handleTouchMove, this);
    this.scene.input.off('pointerup', this.handleTouchEnd, this);
    
    const canvas = this.scene.sys.game.canvas;
    if (canvas) {
      canvas.removeEventListener('touchstart', this.handleNativeTouchStart);
      canvas.removeEventListener('touchmove', this.handleNativeTouchMove);
      canvas.removeEventListener('touchend', this.handleNativeTouchEnd);
    }
    
    this.inputBuffer = [];
    
    console.log('WeChatInputManager destroyed');
  }
}