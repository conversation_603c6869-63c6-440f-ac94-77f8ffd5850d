import { GAME_CONSTANTS } from '../config/GameConfig';

/**
 * WeChat Asset Optimizer
 * Optimizes asset loading and management for WeChat miniGame environment
 */
export class WeChatAssetOptimizer {
  private scene: Phaser.Scene;
  private loadedAssets: Set<string> = new Set();
  private assetQueue: Array<{ key: string; url: string; type: string }> = [];
  private isLoading: boolean = false;
  private maxConcurrentLoads: number = 3;
  private currentLoads: number = 0;

  constructor(scene: Phaser.Scene) {
    this.scene = scene;
    console.log('WeChatAssetOptimizer initialized');
  }

  /**
   * Optimized sprite loading with size constraints
   */
  public loadOptimizedSprite(key: string, url: string, maxSize: number = GAME_CONSTANTS.WECHAT.MAX_TEXTURE_SIZE): Promise<boolean> {
    return new Promise((resolve, reject) => {
      if (this.loadedAssets.has(key)) {
        resolve(true);
        return;
      }

      // Check if we can load immediately or need to queue
      if (this.currentLoads >= this.maxConcurrentLoads) {
        this.assetQueue.push({ key, url, type: 'image' });
        resolve(true);
        return;
      }

      this.currentLoads++;
      
      // Create optimized loading
      this.scene.load.image(key, url);
      
      this.scene.load.once(`filecomplete-image-${key}`, () => {
        // Validate texture size for WeChat
        const texture = this.scene.textures.get(key);
        if (texture && (texture.source[0].width > maxSize || texture.source[0].height > maxSize)) {
          console.warn(`WeChat: Texture ${key} exceeds max size ${maxSize}px`);
          // Could implement automatic resizing here
        }
        
        this.loadedAssets.add(key);
        this.currentLoads--;
        this.processQueue();
        resolve(true);
      });
      
      this.scene.load.once(`loaderror`, () => {
        console.error(`WeChat: Failed to load asset ${key}`);
        this.currentLoads--;
        this.processQueue();
        reject(new Error(`Failed to load ${key}`));
      });
      
      if (!this.isLoading) {
        this.scene.load.start();
      }
    });
  }

  /**
   * Create optimized placeholder textures for WeChat performance
   */
  public createOptimizedPlaceholders(): void {
    const graphics = this.scene.add.graphics();
    
    // Use smaller, optimized placeholder sizes
    const placeholders = [
      { key: 'player', color: 0x00FF00, width: 32, height: 32 },
      { key: 'platform-normal', color: 0x8B4513, width: 80, height: 16 },
      { key: 'platform-spring', color: 0xFF6B6B, width: 80, height: 16 },
      { key: 'platform-moving', color: 0x4ECDC4, width: 80, height: 16 },
      { key: 'platform-breaking', color: 0xFFE66D, width: 80, height: 16 },
      { key: 'platform-disappearing', color: 0xA8E6CF, width: 80, height: 16 }
    ];

    placeholders.forEach(placeholder => {
      // Create optimized placeholder with minimal memory usage
      graphics.clear();
      graphics.fillStyle(placeholder.color);
      graphics.fillRect(0, 0, placeholder.width, placeholder.height);
      
      // Generate texture with WeChat-optimized settings
      graphics.generateTexture(placeholder.key, placeholder.width, placeholder.height);
      
      this.loadedAssets.add(placeholder.key);
    });
    
    graphics.destroy();
    console.log('WeChat: Optimized placeholders created');
  }

  /**
   * Preload critical assets with WeChat optimization
   */
  public preloadCriticalAssets(): Promise<boolean> {
    return new Promise((resolve) => {
      const criticalAssets = [
        'player',
        'platform-normal',
        'platform-spring'
      ];

      // Create optimized placeholders first
      this.createOptimizedPlaceholders();

      // Check if all critical assets are loaded
      const allLoaded = criticalAssets.every(asset => this.loadedAssets.has(asset));
      
      if (allLoaded) {
        console.log('WeChat: All critical assets preloaded');
        resolve(true);
      } else {
        console.log('WeChat: Critical assets created as placeholders');
        resolve(true);
      }
    });
  }

  /**
   * Lazy load non-critical assets
   */
  public lazyLoadAsset(key: string, url: string): void {
    if (this.loadedAssets.has(key)) {
      return;
    }

    // Add to queue for later loading
    this.assetQueue.push({ key, url, type: 'image' });
    
    // Process queue if not currently loading
    if (this.currentLoads < this.maxConcurrentLoads) {
      this.processQueue();
    }
  }

  /**
   * Process asset loading queue
   */
  private processQueue(): void {
    while (this.assetQueue.length > 0 && this.currentLoads < this.maxConcurrentLoads) {
      const asset = this.assetQueue.shift();
      if (asset && !this.loadedAssets.has(asset.key)) {
        this.loadOptimizedSprite(asset.key, asset.url);
      }
    }
  }

  /**
   * Clear unused assets to free memory
   */
  public clearUnusedAssets(keepAssets: string[] = []): void {
    const textureManager = this.scene.textures;
    const defaultTextures = ['__DEFAULT', '__MISSING'];
    const protectedAssets = [...keepAssets, ...defaultTextures];

    Object.keys(textureManager.list).forEach(key => {
      if (!protectedAssets.includes(key) && this.loadedAssets.has(key)) {
        textureManager.remove(key);
        this.loadedAssets.delete(key);
        console.log(`WeChat: Cleared unused asset: ${key}`);
      }
    });

    // Force garbage collection if available
    if ((window as any).gc) {
      (window as any).gc();
    }
  }

  /**
   * Get memory usage estimation
   */
  public getMemoryUsage(): { textureCount: number, estimatedMemory: number } {
    const textureCount = this.loadedAssets.size;
    const estimatedMemory = textureCount * 2048; // Rough estimation in bytes
    
    return { textureCount, estimatedMemory };
  }

  /**
   * Optimize texture for WeChat constraints
   */
  public optimizeTexture(key: string): boolean {
    const texture = this.scene.textures.get(key);
    if (!texture) {
      return false;
    }

    const source = texture.source[0];
    const maxSize = GAME_CONSTANTS.WECHAT.MAX_TEXTURE_SIZE;

    // Check if texture needs optimization
    if (source.width > maxSize || source.height > maxSize) {
      console.warn(`WeChat: Texture ${key} (${source.width}x${source.height}) exceeds limit ${maxSize}px`);
      
      // Could implement texture resizing here
      // For now, just log the warning
      return false;
    }

    return true;
  }

  /**
   * Create sprite atlas for better memory efficiency
   */
  public createSpriteAtlas(sprites: Array<{ key: string, x: number, y: number, width: number, height: number }>): string {
    const atlasKey = 'sprite-atlas';
    const atlasSize = 512; // WeChat-optimized atlas size
    
    const graphics = this.scene.add.graphics();
    
    sprites.forEach(sprite => {
      // Get source texture
      const sourceTexture = this.scene.textures.get(sprite.key);
      if (sourceTexture) {
        // Draw to atlas position
        // This is a simplified version - full implementation would need proper texture copying
        graphics.fillStyle(0xFFFFFF);
        graphics.fillRect(sprite.x, sprite.y, sprite.width, sprite.height);
      }
    });

    // Generate atlas texture
    graphics.generateTexture(atlasKey, atlasSize, atlasSize);
    graphics.destroy();

    this.loadedAssets.add(atlasKey);
    console.log(`WeChat: Sprite atlas '${atlasKey}' created with ${sprites.length} sprites`);
    
    return atlasKey;
  }

  /**
   * Batch load assets for better performance
   */
  public batchLoadAssets(assets: Array<{ key: string, url: string, type?: string }>): Promise<boolean> {
    return new Promise((resolve, reject) => {
      if (assets.length === 0) {
        resolve(true);
        return;
      }

      let loadedCount = 0;
      let errorCount = 0;

      assets.forEach(asset => {
        const type = asset.type || 'image';
        
        if (this.loadedAssets.has(asset.key)) {
          loadedCount++;
          if (loadedCount + errorCount >= assets.length) {
            resolve(errorCount === 0);
          }
          return;
        }

        // Use type-safe loader method calls
        switch (type) {
          case 'image':
            this.scene.load.image(asset.key, asset.url);
            break;
          case 'audio':
            this.scene.load.audio(asset.key, asset.url);
            break;
          case 'json':
            this.scene.load.json(asset.key, asset.url);
            break;
          default:
            console.warn(`Unsupported asset type: ${type}`);
            return; // Return from forEach callback instead of continue
        }
        
        this.scene.load.once(`filecomplete-${type}-${asset.key}`, () => {
          this.loadedAssets.add(asset.key);
          loadedCount++;
          
          if (loadedCount + errorCount >= assets.length) {
            resolve(errorCount === 0);
          }
        });
        
        this.scene.load.once(`loaderror`, () => {
          errorCount++;
          console.error(`WeChat: Failed to load ${asset.key}`);
          
          if (loadedCount + errorCount >= assets.length) {
            resolve(errorCount === 0);
          }
        });
      });

      this.scene.load.start();
    });
  }

  public isAssetLoaded(key: string): boolean {
    return this.loadedAssets.has(key);
  }

  public getLoadedAssets(): string[] {
    return Array.from(this.loadedAssets);
  }

  public destroy(): void {
    this.loadedAssets.clear();
    this.assetQueue = [];
    this.currentLoads = 0;
    console.log('WeChatAssetOptimizer destroyed');
  }
}