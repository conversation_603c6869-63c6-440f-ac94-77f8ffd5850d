// Temporary local constants until shared package is fixed
const GAME_CONFIG = {
  PHYSICS: {
    GRAVITY: 980,
    JUMP_VELOCITY: -450,
    MAX_FALL_SPEED: 800,
    BOUNCE_DAMPING: 0.8,
    FRICTION: 0.95
  },
  PLAYER: {
    SIZE: { width: 32, height: 32 },
    MAX_HORIZONTAL_SPEED: 300,
    ACCELERATION: 500
  },
  PLATFORMS: {
    BASE_WIDTH: 80,
    MIN_GAP: 30,
    MAX_GAP: 120,
    SPECIAL_PLATFORM_CHANCE: 0.15,
    POWER_UP_CHANCE: 0.08
  },
  CAMERA: {
    SMOOTHING: 0.1,
    OFFSET_Y: 200
  }
};

export const gameConfig: Phaser.Types.Core.GameConfig = {
  type: Phaser.CANVAS, // Force Canvas renderer for WeChat compatibility
  width: 375, // Standard mobile width
  height: 667, // Standard mobile height (iPhone 6/7/8 reference)
  parent: 'game-container',
  backgroundColor: '#87CEEB', // Sky blue background
  
  // Physics configuration optimized for WeChat
  physics: {
    default: 'arcade',
    arcade: {
      gravity: { x: 0, y: GAME_CONFIG.PHYSICS.GRAVITY }, // 980 px/s² from shared constants
      debug: false, // Always disable debug in WeChat
      fps: 60,
      fixedStep: false, // Disable for better WeChat performance
      overlapBias: 8, // Increased for WeChat physics stability
      tileBias: 32, // Increased for WeChat collision detection
      timeScale: 1,
      maxEntries: 16 // Limit physics entries for WeChat performance
    }
  },

  // WeChat-optimized render settings
  render: {
    antialias: false, // Critical: Disable for WeChat performance
    pixelArt: true,   // Enable for crisp pixel graphics
    roundPixels: true, // Prevent sub-pixel rendering
    transparent: false, // Disable transparency for performance
    clearBeforeRender: true,
    preserveDrawingBuffer: false, // Disable for memory optimization
    failIfMajorPerformanceCaveat: true, // Fail fast on poor performance
    powerPreference: "low-power", // Optimize for battery life
    batchSize: 2000, // Reduce batch size for WeChat
    maxTextures: 16 // Limit texture units for WeChat WebView
  },

  // Audio configuration
  audio: {
    disableWebAudio: false,
    noAudio: false
  },

  // Scale configuration for responsive design
  scale: {
    mode: Phaser.Scale.FIT,
    autoCenter: Phaser.Scale.CENTER_BOTH,
    width: 375,
    height: 667,
    min: {
      width: 320,
      height: 568
    },
    max: {
      width: 768,
      height: 1024
    }
  },

  // WeChat touch input optimization
  input: {
    activePointers: 1, // Single touch for casual gameplay  
    smoothFactor: 0.1, // Reduced for more responsive touch
    windowEvents: false, // Prevent conflicts with WeChat
    touch: {
      target: 'game-container',
      capture: true
    },
    mouse: {
      preventDefaultDown: true,
      preventDefaultUp: true,
      preventDefaultMove: true,
      preventDefaultWheel: false
    },
    gamepad: false, // Disable gamepad for WeChat
    keyboard: false // Disable keyboard for mobile WeChat
  },

  // WeChat-optimized FPS settings
  fps: {
    target: 60,
    min: 30, // Minimum acceptable FPS for WeChat
    forceSetTimeOut: false, // Use requestAnimationFrame for WeChat
    deltaHistory: 5, // Reduced for memory optimization
    panicMax: 60, // Reduced panic threshold for WeChat
    smoothStep: false // Disable for WeChat performance
  },

  // Scene configuration
  scene: [], // Will be populated when scenes are created

  // WeChat-specific optimizations
  dom: {
    createContainer: false // Disable DOM container for WeChat compatibility
  },

  // Banner configuration
  banner: {
    hidePhaser: process.env.NODE_ENV === 'production',
    text: '#87CEEB',
    background: ['#87CEEB', '#98D8E8', '#B6E2F2']
  }
};

// Game constants derived from shared configuration
export const GAME_CONSTANTS = {
  WORLD: {
    WIDTH: 375,
    HEIGHT: 667,
    BOUNDS_PADDING: 50
  },
  
  PLAYER: {
    SIZE: GAME_CONFIG.PLAYER.SIZE,
    MAX_HORIZONTAL_SPEED: GAME_CONFIG.PLAYER.MAX_HORIZONTAL_SPEED,
    ACCELERATION: GAME_CONFIG.PLAYER.ACCELERATION,
    JUMP_VELOCITY: GAME_CONFIG.PHYSICS.JUMP_VELOCITY,
    FRICTION: GAME_CONFIG.PHYSICS.FRICTION
  },

  PLATFORMS: {
    BASE_WIDTH: GAME_CONFIG.PLATFORMS.BASE_WIDTH,
    MIN_GAP: GAME_CONFIG.PLATFORMS.MIN_GAP,
    MAX_GAP: GAME_CONFIG.PLATFORMS.MAX_GAP,
    SPECIAL_CHANCE: GAME_CONFIG.PLATFORMS.SPECIAL_PLATFORM_CHANCE,
    POWERUP_CHANCE: GAME_CONFIG.PLATFORMS.POWER_UP_CHANCE
  },

  PHYSICS: {
    GRAVITY: GAME_CONFIG.PHYSICS.GRAVITY,
    JUMP_VELOCITY: GAME_CONFIG.PHYSICS.JUMP_VELOCITY,
    MAX_FALL_SPEED: GAME_CONFIG.PHYSICS.MAX_FALL_SPEED,
    BOUNCE_DAMPING: GAME_CONFIG.PHYSICS.BOUNCE_DAMPING
  },

  CAMERA: {
    SMOOTHING: GAME_CONFIG.CAMERA.SMOOTHING,
    OFFSET_Y: GAME_CONFIG.CAMERA.OFFSET_Y
  },

  PERFORMANCE: {
    TARGET_FPS: 60,
    MIN_FPS: 30, // WeChat minimum acceptable FPS
    OBJECT_POOL_SIZE: 30, // Reduced for WeChat memory constraints
    MAX_PLATFORMS_VISIBLE: 12, // Reduced for WeChat performance
    GARBAGE_COLLECT_INTERVAL: 3000, // 3 seconds for WeChat
    MAX_PARTICLES: 20, // Limit particle effects
    TEXTURE_CACHE_SIZE: 64, // Limit texture cache for memory
    MEMORY_WARNING_THRESHOLD: 50 * 1024 * 1024, // 50MB warning threshold
    PERFORMANCE_MONITOR_INTERVAL: 1000, // Monitor every second
    CANVAS_CLEAR_FREQUENCY: 30, // Clear canvas every 30 frames
    PHYSICS_STEP_SIZE: 1/60, // Fixed physics step for consistency
    MAX_DELTA_TIME: 1000/30 // Cap delta time for stability
  },

  // WeChat-specific constants
  WECHAT: {
    MAX_CANVAS_SIZE: 2048, // WeChat canvas size limit
    MAX_TEXTURE_SIZE: 1024, // WeChat texture size limit  
    MEMORY_LIMIT: 128 * 1024 * 1024, // 128MB memory limit
    TOUCH_SENSITIVITY: 0.8, // Touch sensitivity adjustment
    PAUSE_ON_BLUR: true, // Pause when app loses focus
    RESUME_DELAY: 100, // Delay before resume (ms)
    PRELOAD_AUDIO_IOS: false, // Disable iOS audio preload in WeChat
    ENABLE_DEVICE_PIXEL_RATIO: false // Disable DPR for WeChat performance
  }
};

export default gameConfig;