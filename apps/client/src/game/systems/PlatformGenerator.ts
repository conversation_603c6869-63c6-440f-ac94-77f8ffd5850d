import { PlatformType } from '../../types/game';
import { Platform } from '../entities/Platform';
import { GAME_CONSTANTS } from '../config/GameConfig';

export class PlatformGenerator {
  private physics: Phaser.Physics.Arcade.ArcadePhysics;
  private platformPool: Platform[];
  private scene: Phaser.Scene;
  
  // Generation state
  private lastPlatformY: number = 0;
  private nextPlatformId: number = 0;
  private generatedPlatforms: number = 0;
  
  // Generation parameters
  private readonly baseGap: number = GAME_CONSTANTS.PLATFORMS.MIN_GAP;
  private readonly maxGap: number = GAME_CONSTANTS.PLATFORMS.MAX_GAP;
  private readonly platformWidth: number = GAME_CONSTANTS.PLATFORMS.BASE_WIDTH;
  private readonly worldWidth: number = GAME_CONSTANTS.WORLD.WIDTH;
  
  // Platform distribution chances
  private readonly specialChance: number = GAME_CONSTANTS.PLATFORMS.SPECIAL_CHANCE;
  
  // Difficulty scaling
  private difficultyMultiplier: number = 1.0;
  private readonly maxDifficulty: number = 2.0;
  private readonly difficultyIncreaseRate: number = 0.001; // Per platform generated

  constructor(physics: Phaser.Physics.Arcade.ArcadePhysics, platformPool: Platform[]) {
    // Note: We'll get the scene reference when we need it from the physics world
    this.physics = physics;
    this.platformPool = platformPool;
    this.scene = physics.world.scene; // Get scene from physics world
    
    console.log('PlatformGenerator initialized');
  }

  public generateInitialPlatforms(platformGroup: Phaser.GameObjects.Group, gameStatePlatforms: Platform[]): void {
    console.log('Generating initial platform ladder');
    
    // Set starting position for generation
    this.lastPlatformY = GAME_CONSTANTS.WORLD.HEIGHT - 100; // Start just above the starting platform
    
    // Generate enough platforms to fill the initial view plus some buffer
    const platformsToGenerate = 15;
    
    for (let i = 0; i < platformsToGenerate; i++) {
      const platform = this.generateNextPlatform();
      if (platform) {
        platformGroup.add(platform);
        gameStatePlatforms.push(platform);
      }
    }
    
    console.log(`Generated ${platformsToGenerate} initial platforms`);
  }

  public update(playerY: number): void {
    // Generate new platforms as player climbs higher
    const generateThreshold = this.lastPlatformY + (GAME_CONSTANTS.WORLD.HEIGHT * 1.5);
    
    if (playerY < generateThreshold) {
      // Generate platforms ahead of the player
      const platformsToGenerate = 5; // Generate in batches for performance
      
      for (let i = 0; i < platformsToGenerate; i++) {
        const platform = this.generateNextPlatform();
        if (platform) {
          // Add to scene groups (this would be handled by the GameScene)
          this.onPlatformGenerated(platform);
        }
      }
    }
  }

  private generateNextPlatform(): Platform | null {
    // Calculate next platform position
    const position = this.calculateNextPlatformPosition();
    if (!position) return null;
    
    // Determine platform type
    const type = this.determinePlatformType();
    
    // Create or reuse platform from pool
    const platform = this.createPlatform(position.x, position.y, type);
    
    // Update generation state
    this.lastPlatformY = position.y;
    this.generatedPlatforms++;
    this.updateDifficulty();
    
    return platform;
  }

  private calculateNextPlatformPosition(): { x: number; y: number } | null {
    // Calculate vertical gap based on difficulty
    const baseVerticalGap = this.baseGap + (this.maxGap - this.baseGap) * 0.3;
    const difficultyVariance = (this.maxGap - this.baseGap) * 0.7 * this.difficultyMultiplier;
    const verticalGap = baseVerticalGap + (Math.random() * difficultyVariance);
    
    // Ensure gap doesn't exceed maximum
    const clampedGap = Math.min(verticalGap, this.maxGap);
    
    // Calculate Y position (negative because we're going up)
    const y = this.lastPlatformY - clampedGap;
    
    // Calculate X position with some randomness
    const margin = this.platformWidth / 2;
    const availableWidth = this.worldWidth - (margin * 2);
    const x = margin + (Math.random() * availableWidth);
    
    return { x, y };
  }

  private determinePlatformType(): PlatformType {
    // Increase special platform chance with difficulty
    const adjustedSpecialChance = this.specialChance * (1 + this.difficultyMultiplier * 0.5);
    
    if (Math.random() > adjustedSpecialChance) {
      return PlatformType.NORMAL;
    }
    
    // Distribute special platform types
    const specialRoll = Math.random();
    
    if (specialRoll < 0.4) {
      return PlatformType.SPRING; // 40% of special platforms
    } else if (specialRoll < 0.65) {
      return PlatformType.MOVING; // 25% of special platforms
    } else if (specialRoll < 0.85) {
      return PlatformType.BREAKING; // 20% of special platforms
    } else {
      return PlatformType.DISAPPEARING; // 15% of special platforms
    }
  }

  private createPlatform(x: number, y: number, type: PlatformType): Platform {
    // Try to reuse from pool first
    let platform = this.getFromPool();
    
    if (platform) {
      // Reset existing platform
      platform.reset(x, y, type);
    } else {
      // Create new platform
      platform = new Platform(this.scene, x, y, type);
    }
    
    return platform;
  }

  private getFromPool(): Platform | null {
    return this.platformPool.pop() || null;
  }

  private updateDifficulty(): void {
    // Gradually increase difficulty as more platforms are generated
    this.difficultyMultiplier = Math.min(
      this.maxDifficulty,
      1.0 + (this.generatedPlatforms * this.difficultyIncreaseRate)
    );
    
    // Log difficulty changes
    if (this.generatedPlatforms % 50 === 0) {
      console.log(`Difficulty updated: ${this.difficultyMultiplier.toFixed(2)} after ${this.generatedPlatforms} platforms`);
    }
  }

  private onPlatformGenerated(platform: Platform): void {
    // This method would be called by GameScene to add the platform to groups
    // For now, we'll just log the generation
    console.log(`Platform generated: ${platform.type} at (${platform.x.toFixed(1)}, ${platform.y.toFixed(1)})`);
  }

  // Public methods for external control
  public setDifficulty(multiplier: number): void {
    this.difficultyMultiplier = Math.max(1.0, Math.min(this.maxDifficulty, multiplier));
    console.log(`Difficulty manually set to: ${this.difficultyMultiplier}`);
  }

  public getDifficulty(): number {
    return this.difficultyMultiplier;
  }

  public getGeneratedCount(): number {
    return this.generatedPlatforms;
  }

  public getLastPlatformY(): number {
    return this.lastPlatformY;
  }

  // Platform spacing validation (for testing)
  public validatePlatformSpacing(platforms: Platform[]): boolean {
    if (platforms.length < 2) return true;
    
    for (let i = 1; i < platforms.length; i++) {
      const gap = Math.abs(platforms[i-1].y - platforms[i].y);
      if (gap < this.baseGap || gap > this.maxGap) {
        console.warn(`Invalid platform spacing: ${gap}px (min: ${this.baseGap}, max: ${this.maxGap})`);
        return false;
      }
    }
    
    return true;
  }

  // Generate platforms for a specific height range (useful for testing)
  public generatePlatformsInRange(startY: number, endY: number, platformGroup?: Phaser.GameObjects.Group): Platform[] {
    const platforms: Platform[] = [];
    this.lastPlatformY = startY;
    
    while (this.lastPlatformY > endY) {
      const platform = this.generateNextPlatform();
      if (platform) {
        platforms.push(platform);
        if (platformGroup) {
          platformGroup.add(platform);
        }
      } else {
        break; // Safety break
      }
    }
    
    console.log(`Generated ${platforms.length} platforms in range ${startY} to ${endY}`);
    return platforms;
  }

  // Reset generator state (for game restart)
  public reset(): void {
    this.lastPlatformY = 0;
    this.nextPlatformId = 0;
    this.generatedPlatforms = 0;
    this.difficultyMultiplier = 1.0;
    
    console.log('PlatformGenerator reset');
  }

  // Cleanup
  public destroy(): void {
    // Clear platform pool
    this.platformPool.forEach(platform => platform.destroy());
    this.platformPool.length = 0;
    
    console.log('PlatformGenerator destroyed');
  }
}