import { Player } from '../entities/Player';

export class InputSystem {
  private input: Phaser.Input.InputPlugin;
  private player: Player | null = null;
  private enabled: boolean = false;

  constructor(input: Phaser.Input.InputPlugin) {
    this.input = input;
    console.log('InputSystem initialized');
  }

  public setPlayer(player: Player): void {
    this.player = player;
  }

  public enable(): void {
    this.enabled = true;
    console.log('InputSystem enabled');
  }

  public disable(): void {
    this.enabled = false;
    if (this.player) {
      this.player.setHorizontalInput(0);
    }
    console.log('InputSystem disabled');
  }

  public update(delta: number): void {
    if (!this.enabled || !this.player) return;

    // Handle keyboard input (for development/testing)
    const cursors = this.input.keyboard?.createCursorKeys();
    if (cursors) {
      let horizontalInput = 0;
      
      if (cursors.left?.isDown) {
        horizontalInput = -1;
      } else if (cursors.right?.isDown) {
        horizontalInput = 1;
      }
      
      this.player.setHorizontalInput(horizontalInput);
    }

    // Touch input will be handled by WeChatInputManager
    // This is a basic fallback implementation
  }

  public destroy(): void {
    this.enabled = false;
    this.player = null;
    console.log('InputSystem destroyed');
  }
}