import * as Phaser from 'phaser';
import { gameConfig } from './game/config/GameConfig';
import { GameScene } from './game/scenes/GameScene';
// WeChat types are now declared globally in wechat.d.ts

// WeChat miniGame initialization
declare global {
  interface Window {
    updateLoadingProgress?: (progress: number, message: string) => void;
    hideLoading?: () => void;
    showError?: (message: string) => void;
    gc?: () => void;
  }
}

class WeChatDoodleJump {
  private game: Phaser.Game | null = null;
  private gameContainer: HTMLElement | null = null;

  constructor() {
    this.init();
  }

  private async init(): Promise<void> {
    try {
      console.log('Initializing WeChat Doodle Jump miniGame...');
      
      // Show loading progress
      this.updateProgress(10, '正在初始化游戏引擎...');
      
      // Setup game container
      this.setupGameContainer();
      
      // Initialize Phaser game
      await this.initializeGame();
      
      // Setup WeChat-specific event handlers
      this.setupWeChatHandlers();
      
      console.log('WeChat Doodle Jump initialized successfully');
      
    } catch (error) {
      console.error('Failed to initialize game:', error);
      this.showError('游戏初始化失败，请重新加载页面。');
    }
  }

  private setupGameContainer(): void {
    // Create or find game container
    this.gameContainer = document.getElementById('game-container');
    
    if (!this.gameContainer) {
      this.gameContainer = document.createElement('div');
      this.gameContainer.id = 'game-container';
      this.gameContainer.style.cssText = `
        width: 100vw;
        height: 100vh;
        margin: 0;
        padding: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        background: #87CEEB;
        overflow: hidden;
      `;
      document.body.appendChild(this.gameContainer);
    }

    // Setup body styles for mobile
    document.body.style.cssText = `
      margin: 0;
      padding: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;
      background: #87CEEB;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;

    this.updateProgress(20, '正在设置游戏容器...');
  }

  private async initializeGame(): Promise<void> {
    this.updateProgress(30, '正在加载游戏场景...');
    
    // Configure game with scenes
    const config: Phaser.Types.Core.GameConfig = {
      ...gameConfig,
      scene: [GameScene], // Add GameScene to configuration
      parent: this.gameContainer
    };

    // Create Phaser game instance
    this.game = new Phaser.Game(config);

    // Wait for game to be ready
    return new Promise((resolve, reject) => {
      if (!this.game) {
        reject(new Error('Failed to create game instance'));
        return;
      }

      // Listen for game ready event
      this.game.events.once('ready', () => {
        console.log('Phaser game ready');
        this.updateProgress(80, '游戏加载完成');
        resolve();
      });

      // Handle game creation errors
      this.game.events.once('error', (error: Error) => {
        console.error('Phaser game error:', error);
        reject(error);
      });

      // Timeout fallback
      setTimeout(() => {
        if (this.game && this.game.isRunning) {
          console.log('Game initialization timeout - assuming success');
          this.updateProgress(80, '游戏加载完成');
          resolve();
        } else {
          reject(new Error('Game initialization timeout'));
        }
      }, 10000);
    });
  }

  private setupWeChatHandlers(): void {
    this.updateProgress(90, '正在设置微信接口...');

    // Handle WeChat app lifecycle events
    if (typeof wx !== 'undefined') {
      // WeChat miniGame API available
      wx.onShow(() => {
        console.log('WeChat app resumed');
        if (this.game && this.game.scene.isActive('GameScene')) {
          const gameScene = this.game.scene.getScene('GameScene') as any;
          if (gameScene && gameScene.resumeGame) {
            gameScene.resumeGame();
          }
        }
      });

      wx.onHide(() => {
        console.log('WeChat app paused');
        if (this.game && this.game.scene.isActive('GameScene')) {
          const gameScene = this.game.scene.getScene('GameScene') as any;
          if (gameScene && gameScene.pauseGame) {
            gameScene.pauseGame();
          }
        }
      });

      // Handle memory warnings
      wx.onMemoryWarning(() => {
        console.warn('WeChat memory warning - triggering cleanup');
        if (window.gc) {
          window.gc();
        }
        
        // Trigger game cleanup if available
        if (this.game && this.game.scene.isActive('GameScene')) {
          const gameScene = this.game.scene.getScene('GameScene') as any;
          if (gameScene && gameScene.performGarbageCollection) {
            gameScene.performGarbageCollection();
          }
        }
      });

      // Handle audio interruption
      wx.onAudioInterruptionBegin(() => {
        console.log('Audio interrupted - pausing game');
        if (this.game && this.game.sound) {
          this.game.sound.pauseAll();
        }
      });

      wx.onAudioInterruptionEnd(() => {
        console.log('Audio interruption ended - resuming game');
        if (this.game && this.game.sound) {
          this.game.sound.resumeAll();
        }
      });
    }

    // Handle page visibility changes (fallback for non-WeChat environments)
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        console.log('Page hidden - pausing game');
        if (this.game && this.game.scene.isActive('GameScene')) {
          const gameScene = this.game.scene.getScene('GameScene') as any;
          if (gameScene && gameScene.pauseGame) {
            gameScene.pauseGame();
          }
        }
      } else {
        console.log('Page visible - resuming game');
        if (this.game && this.game.scene.isActive('GameScene')) {
          const gameScene = this.game.scene.getScene('GameScene') as any;
          if (gameScene && gameScene.resumeGame) {
            gameScene.resumeGame();
          }
        }
      }
    });

    this.updateProgress(100, '初始化完成');
  }

  private updateProgress(progress: number, message: string): void {
    if (window.updateLoadingProgress) {
      window.updateLoadingProgress(progress, message);
    }
    console.log(`Loading: ${progress}% - ${message}`);
  }

  private showError(message: string): void {
    if (window.showError) {
      window.showError(message);
    } else {
      alert(message);
    }
  }

  // Public methods for external control
  public getGame(): Phaser.Game | null {
    return this.game;
  }

  public restart(): void {
    if (this.game && this.game.scene.isActive('GameScene')) {
      const gameScene = this.game.scene.getScene('GameScene') as any;
      if (gameScene && gameScene.restartGame) {
        gameScene.restartGame();
      }
    }
  }

  public destroy(): void {
    if (this.game) {
      this.game.destroy(true);
      this.game = null;
    }
  }
}

// Initialize the game when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    new WeChatDoodleJump();
  });
} else {
  new WeChatDoodleJump();
}

// Export for global access
(window as any).WeChatDoodleJump = WeChatDoodleJump;