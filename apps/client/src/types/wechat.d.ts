// WeChat miniGame API type definitions
declare global {
  interface Window {
    wx?: typeof wx;
    updateLoadingProgress?: (progress: number, message: string) => void;
    hideLoading?: () => void;
    showError?: (message: string) => void;
    gc?: () => void;
  }

  const wx: {
    // App lifecycle
    onShow(callback: (options?: any) => void): void;
    onHide(callback: () => void): void;
    onError(callback: (error: string) => void): void;
    
    // Memory management
    onMemoryWarning(callback: () => void): void;
    
    // Audio
    onAudioInterruptionBegin(callback: () => void): void;
    onAudioInterruptionEnd(callback: () => void): void;
    
    // Device info
    getSystemInfo(options: {
      success?: (res: {
        brand: string;
        model: string;
        pixelRatio: number;
        screenWidth: number;
        screenHeight: number;
        windowWidth: number;
        windowHeight: number;
        statusBarHeight: number;
        language: string;
        version: string;
        system: string;
        platform: string;
        fontSizeSetting: number;
        SDKVersion: string;
        benchmarkLevel: number;
        albumAuthorized: boolean;
        cameraAuthorized: boolean;
        locationAuthorized: boolean;
        microphoneAuthorized: boolean;
        notificationAuthorized: boolean;
        bluetoothEnabled: boolean;
        locationEnabled: boolean;
        wifiEnabled: boolean;
        safeArea: {
          left: number;
          right: number;
          top: number;
          bottom: number;
          width: number;
          height: number;
        };
      }) => void;
      fail?: (error: any) => void;
      complete?: () => void;
    }): void;
    
    getSystemInfoSync(): {
      brand: string;
      model: string;
      pixelRatio: number;
      screenWidth: number;
      screenHeight: number;
      windowWidth: number;
      windowHeight: number;
      statusBarHeight: number;
      language: string;
      version: string;
      system: string;
      platform: string;
      fontSizeSetting: number;
      SDKVersion: string;
      benchmarkLevel: number;
      albumAuthorized: boolean;
      cameraAuthorized: boolean;
      locationAuthorized: boolean;
      microphoneAuthorized: boolean;
      notificationAuthorized: boolean;
      bluetoothEnabled: boolean;
      locationEnabled: boolean;
      wifiEnabled: boolean;
      safeArea: {
        left: number;
        right: number;  
        top: number;
        bottom: number;
        width: number;
        height: number;
      };
    };
    
    // Performance
    getPerformance(): {
      now(): number;
    };
    
    // Touch and input
    onTouchStart(callback: (event: TouchEvent) => void): void;
    onTouchMove(callback: (event: TouchEvent) => void): void;
    onTouchEnd(callback: (event: TouchEvent) => void): void;
    onTouchCancel(callback: (event: TouchEvent) => void): void;
    
    // Accelerometer
    startAccelerometer(options?: {
      interval?: 'game' | 'ui' | 'normal';
      success?: () => void;
      fail?: (error: any) => void;
      complete?: () => void;
    }): void;
    
    stopAccelerometer(options?: {
      success?: () => void;
      fail?: (error: any) => void;
      complete?: () => void;
    }): void;
    
    onAccelerometerChange(callback: (res: {
      x: number;
      y: number;
      z: number;
    }) => void): void;
    
    // Device orientation
    startDeviceMotionListening(options?: {
      interval?: 'game' | 'ui' | 'normal';
      success?: () => void;
      fail?: (error: any) => void;
      complete?: () => void;
    }): void;
    
    stopDeviceMotionListening(options?: {
      success?: () => void;
      fail?: (error: any) => void;
      complete?: () => void;
    }): void;
    
    onDeviceMotionChange(callback: (res: {
      alpha: number;
      beta: number;
      gamma: number;
    }) => void): void;
    
    // Vibration
    vibrateShort(options?: {
      success?: () => void;
      fail?: (error: any) => void;
      complete?: () => void;
    }): void;
    
    vibrateLong(options?: {
      success?: () => void;
      fail?: (error: any) => void;
      complete?: () => void;
    }): void;
    
    // Storage
    setStorage(options: {
      key: string;
      data: any;
      success?: () => void;
      fail?: (error: any) => void;
      complete?: () => void;
    }): void;
    
    getStorage(options: {
      key: string;
      success?: (res: { data: any }) => void;
      fail?: (error: any) => void;
      complete?: () => void;
    }): void;
    
    removeStorage(options: {
      key: string;
      success?: () => void;
      fail?: (error: any) => void;
      complete?: () => void;
    }): void;
    
    // Social features
    shareAppMessage(options?: {
      title?: string;
      imageUrl?: string;
      query?: string;
      success?: () => void;
      fail?: (error: any) => void;
      complete?: () => void;
    }): void;
    
    showShareMenu(options?: {
      withShareTicket?: boolean;
      success?: () => void;
      fail?: (error: any) => void;
      complete?: () => void;
    }): void;
    
    // User info
    getUserInfo(options: {
      withCredentials?: boolean;
      lang?: string;
      success?: (res: {
        userInfo: {
          nickName: string;
          avatarUrl: string;
          gender: number;
          city: string;
          province: string;
          country: string;
          language: string;
        };
        rawData: string;
        signature: string;
        encryptedData: string;
        iv: string;
      }) => void;
      fail?: (error: any) => void;
      complete?: () => void;
    }): void;
    
    // Analytics
    reportAnalytics(eventName: string, data: Record<string, any>): void;
  };
}

export {};