// Local game type definitions to avoid shared package dependency during build

export enum PlayerState {
  IDLE = 'idle',
  JUMPING = 'jumping',
  FALLING = 'falling',
  LANDING = 'landing',
  DEAD = 'dead'
}

export enum PlatformType {
  NORMAL = 'normal',
  SPRING = 'spring',
  MOVING = 'moving',
  BREAKING = 'breaking',
  DISAPPEARING = 'disappearing'
}

export interface GameState {
  isPlaying: boolean;
  isPaused: boolean;
  score: number;
  platforms: Platform[];
  player: Player | null;
  powerUps: ActivePowerUp[];
  camera: Camera;
  gameTime: number;
}

export interface Platform {
  id: string;
  x: number;
  y: number;
  width: number;
  type: PlatformType;
  isMoving?: boolean;
  movementSpeed?: number;
  movementRange?: number;
}

export interface Player {
  id: string;
  position: { x: number; y: number };
  velocity: { x: number; y: number };
  state: PlayerState;
  skin: PlayerSkin;
  animations: { current: string; frame: number; speed: number };
  effects: ActiveEffect[];
}

export interface PlayerData {
  id: string;
  position: { x: number; y: number };
  velocity: { x: number; y: number };
  state: PlayerState;
  skin: PlayerSkin;
  animations: { current: string; frame: number; speed: number };
  effects: ActiveEffect[];
}

export interface PlayerSkin {
  id: string;
  name: string;
  texture: string;
  animations: Record<string, any>;
  unlocked: boolean;
}

export interface ActivePowerUp {
  id: string;
  type: string;
  duration: number;
  startTime: number;
}

export interface ActiveEffect {
  id: string;
  type: string;
  duration: number;
  startTime: number;
}

export interface Camera {
  x: number;
  y: number;
  zoom: number;
}