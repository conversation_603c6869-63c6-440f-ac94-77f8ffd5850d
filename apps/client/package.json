{"name": "@wechat-doodle/client", "version": "1.0.0", "description": "WeChat MiniGame Client - Doodle Jump Clone", "main": "dist/game.js", "scripts": {"dev": "webpack serve --mode development", "build": "webpack --mode production", "typecheck": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src --ext .ts,.tsx"}, "dependencies": {"axios": "^1.6.7", "phaser": "^3.70.0", "socket.io-client": "^4.7.4"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "^20.11.5", "copy-webpack-plugin": "^12.0.2", "html-webpack-plugin": "^5.6.0", "jest": "^29.7.0", "jest-canvas-mock": "^2.5.2", "jest-environment-jsdom": "^30.0.5", "terser-webpack-plugin": "^5.3.10", "ts-jest": "^29.1.1", "ts-loader": "^9.5.1", "typescript": "^5.3.3", "webpack": "^5.89.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1"}, "jest": {"preset": "ts-jest", "testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/tests/setup.ts"], "testMatch": ["**/__tests__/**/*.test.ts"], "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts"]}}