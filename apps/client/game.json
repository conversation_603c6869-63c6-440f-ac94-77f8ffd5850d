{"deviceOrientation": "portrait", "showStatusBar": false, "networkTimeout": {"request": 8000, "connectSocket": 8000, "uploadFile": 15000, "downloadFile": 15000}, "subpackages": [{"root": "assets/", "name": "assets", "pages": []}], "workers": "workers", "requiredBackgroundModes": [], "plugins": {}, "preloadRule": {"assets/": {"network": "all", "packages": ["assets"]}}, "resizable": false, "navigateToMiniProgramAppIdList": [], "permission": {"scope.userLocation": {"desc": "用于排行榜地区功能"}}, "lazyCodeLoading": "requiredComponents", "renderer": "canvas2d", "rendererOptions": {"antialias": false, "alpha": false, "preserveDrawingBuffer": false, "powerPreference": "low-power"}, "performance": {"dropFrame": true, "subpackages": true}, "style": "v2", "useExtendedLib": {"weui": false}, "entranceDeclare": {"locationMessage": {"path": "pages/index", "query": ""}}, "darkmode": false, "themeLocation": "", "singlePage": {"navigationBarFit": "squeezed"}}