# WeChat Developer Tools Setup Guide

This guide explains how to import and run the WeChat Doodle Jump MiniGame in WeChat Developer Tools.

## Prerequisites

1. **WeChat Developer Tools** - Download from [WeChat Official Developer Platform](https://developers.weixin.qq.com/miniprogram/dev/devtools/download.html)
2. **WeChat Developer Account** - Register at [WeChat Open Platform](https://open.weixin.qq.com/)

## Project Setup

### 1. Build the Project

First, ensure the project is built:

```bash
cd apps/client
npm install
npm run build
```

### 2. Import into WeChat Developer Tools

1. Open WeChat Developer Tools
2. Click "Import Project" (导入项目)
3. Configure the project:
   - **Project Directory**: Select `apps/client/dist/` folder
   - **AppID**: `wx3587a6c5a3c4f177` (or use your own AppID)
   - **Project Name**: `WeChat Doodle Jump`
   - **Project Type**: Select "MiniGame" (小游戏)

### 3. Project Configuration

The project includes these key configuration files:

- `game.json` - MiniGame configuration
- `project.config.json` - WeChat Developer Tools settings
- `app.js` - MiniGame entry point
- `game.js` - Main game bundle
- `game.html` - Game HTML template

### 4. Development Settings

In WeChat Developer Tools, ensure these settings are enabled:

1. **Details Tab** (详情):
   - ✅ Enable ES6 to ES5 compilation
   - ✅ Enable enhanced compilation
   - ✅ Enable PostCSS
   - ❌ Disable URL check (for development)
   - ❌ Disable TLS check (for development)

2. **Local Settings** (本地设置):
   - ✅ Enable hot reload
   - ✅ Enable auto-save
   - ✅ Enable source map

## Game Features

### Core Gameplay
- **Player Movement**: Touch left/right sides of screen to move
- **Jumping**: Automatic jumping when landing on platforms
- **Platform Types**:
  - Normal platforms (brown)
  - Spring platforms (red) - higher bounce
  - Moving platforms (teal) - horizontal movement
  - Breaking platforms (yellow) - break after landing
  - Disappearing platforms (green) - fade after time

### WeChat Integration
- **Lifecycle Management**: Proper pause/resume on app hide/show
- **Memory Optimization**: Automatic cleanup and garbage collection
- **Performance Monitoring**: FPS and memory usage tracking
- **Touch Controls**: Optimized for WeChat WebView

## Troubleshooting

### Common Issues

1. **"Project directory is invalid"**
   - Ensure you're selecting the `dist/` folder, not the root project folder
   - Make sure `npm run build` completed successfully

2. **"AppID is invalid"**
   - Use the provided AppID: `wx3587a6c5a3c4f177`
   - Or register your own AppID and update `project.config.json`

3. **Game doesn't load**
   - Check the Console tab for JavaScript errors
   - Ensure all assets are present in `dist/assets/`
   - Verify `game.js` and `app.js` are in the dist folder

4. **Performance issues**
   - The game is optimized for WeChat constraints
   - Monitor memory usage in the Performance tab
   - Check FPS display in the top-left corner

### Debug Mode

To enable debug mode:
1. Open Console tab in WeChat Developer Tools
2. Look for initialization logs
3. Check for any error messages
4. Monitor performance metrics

## File Structure

```
dist/
├── app.js                 # WeChat MiniGame entry point
├── game.js               # Main game bundle (20MB+)
├── game.html             # Game HTML template
├── game.json             # MiniGame configuration
├── project.config.json   # Developer Tools settings
└── assets/               # Game assets
    ├── images/           # Sprite placeholders
    └── audio/            # Audio placeholders
```

## Performance Notes

- **Bundle Size**: ~21MB (within WeChat's limits)
- **Memory Usage**: Optimized for <128MB limit
- **FPS Target**: 60fps with graceful degradation to 30fps
- **Canvas Renderer**: Canvas2D (required for WeChat)

## Next Steps

1. **Test Basic Functionality**: Verify game loads and player can move
2. **Test Platform Collision**: Ensure jumping mechanics work
3. **Test WeChat Integration**: Test pause/resume functionality
4. **Performance Testing**: Monitor FPS and memory usage
5. **Asset Replacement**: Replace placeholder assets with final art

## Support

For issues with this setup:
1. Check the Console tab for error messages
2. Verify all build steps completed successfully
3. Ensure WeChat Developer Tools is up to date
4. Test with the provided AppID first before using custom AppID
