{"extends": "../../tsconfig.json", "compilerOptions": {"outDir": "./dist", "lib": ["ES2020", "DOM", "DOM.Iterable"], "types": ["node", "jest"], "jsx": "preserve", "baseUrl": "./src", "paths": {"@/*": ["./*"], "@components/*": ["./components/*"], "@services/*": ["./services/*"], "@stores/*": ["./stores/*"], "@utils/*": ["./utils/*"], "@game/*": ["./game/*"], "@wechat-doodle/shared": ["../../packages/shared/src/index"], "@wechat-doodle/shared/*": ["../../packages/shared/src/*"]}}, "include": ["src/**/*", "../../packages/shared/src/**/*"], "exclude": ["node_modules", "dist", "tests"]}