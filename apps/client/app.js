/**
 * WeChat MiniGame Entry Point
 * This file serves as the main entry point for the WeChat MiniGame
 */

// WeChat MiniGame App Configuration
App({
  onLaunch(options) {
    console.log('WeChat MiniGame launched with options:', options);
    
    // Initialize WeChat-specific features
    this.initializeWeChatFeatures();
    
    // Load the main game
    this.loadGame();
  },

  onShow(options) {
    console.log('WeChat MiniGame shown with options:', options);
    
    // Resume game if it was paused
    if (window.WeChatDoodleJump && window.WeChatDoodleJump.getGame) {
      const game = window.WeChatDoodleJump.getGame();
      if (game && game.scene) {
        game.scene.resume();
      }
    }
  },

  onHide() {
    console.log('WeChat MiniGame hidden');
    
    // Pause game to save resources
    if (window.WeChatDoodleJump && window.WeChatDoodleJump.getGame) {
      const game = window.WeChatDoodleJump.getGame();
      if (game && game.scene) {
        game.scene.pause();
      }
    }
  },

  onError(error) {
    console.error('WeChat MiniGame error:', error);
    
    // Report error to WeChat analytics if available
    if (wx && wx.reportAnalytics) {
      wx.reportAnalytics('game_error', {
        error: error.toString(),
        timestamp: Date.now()
      });
    }
  },

  initializeWeChatFeatures() {
    // Initialize WeChat-specific features
    if (typeof wx !== 'undefined') {
      // Setup memory warning handler
      wx.onMemoryWarning(() => {
        console.warn('WeChat memory warning received');
        if (window.gc) {
          window.gc();
        }
      });

      // Setup audio interruption handlers
      wx.onAudioInterruptionBegin(() => {
        console.log('Audio interrupted');
        if (window.WeChatDoodleJump && window.WeChatDoodleJump.getGame) {
          const game = window.WeChatDoodleJump.getGame();
          if (game && game.sound) {
            game.sound.pauseAll();
          }
        }
      });

      wx.onAudioInterruptionEnd(() => {
        console.log('Audio interruption ended');
        if (window.WeChatDoodleJump && window.WeChatDoodleJump.getGame) {
          const game = window.WeChatDoodleJump.getGame();
          if (game && game.sound) {
            game.sound.resumeAll();
          }
        }
      });

      // Get system info for optimization
      try {
        const systemInfo = wx.getSystemInfoSync();
        console.log('WeChat system info:', systemInfo);
        
        // Store system info globally for game optimization
        window.wechatSystemInfo = systemInfo;
      } catch (error) {
        console.error('Failed to get WeChat system info:', error);
      }
    }
  },

  loadGame() {
    // The actual game loading is handled by the webpack-generated game.js
    // This just ensures the environment is ready
    console.log('WeChat MiniGame environment initialized, loading game...');
  },

  // Global data for the app
  globalData: {
    userInfo: null,
    gameSettings: {
      soundEnabled: true,
      musicEnabled: true,
      vibrationEnabled: true
    }
  }
});
