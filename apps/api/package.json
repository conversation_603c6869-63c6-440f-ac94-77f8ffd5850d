{"name": "@wechat-doodle/api", "version": "1.0.0", "description": "WeChat Doodle Jump API Server", "main": "dist/server.js", "scripts": {"dev": "nodemon src/server.ts", "build": "tsc", "start": "node dist/server.js", "typecheck": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:integration": "jest --config jest.integration.config.js", "lint": "eslint src --ext .ts"}, "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.4", "mongoose": "^8.0.4", "redis": "^4.6.12", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "dotenv": "^16.3.1", "winston": "^3.11.0", "compression": "^1.7.4"}, "devDependencies": {"@types/express": "^4.17.21", "@types/node": "^20.11.5", "@types/jsonwebtoken": "^9.0.5", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/jest": "^29.5.11", "@types/supertest": "^6.0.2", "typescript": "^5.3.3", "nodemon": "^3.0.2", "ts-node": "^10.9.2", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.4"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "setupFilesAfterEnv": ["<rootDir>/tests/setup.ts"], "testMatch": ["**/__tests__/**/*.test.ts"], "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts"]}}