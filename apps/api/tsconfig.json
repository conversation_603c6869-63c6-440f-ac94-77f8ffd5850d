{"extends": "../../tsconfig.json", "compilerOptions": {"outDir": "./dist", "rootDir": "./src", "lib": ["ES2020"], "types": ["node", "jest"], "baseUrl": "./src", "paths": {"@/*": ["./*"], "@routes/*": ["./routes/*"], "@services/*": ["./services/*"], "@models/*": ["./models/*"], "@middleware/*": ["./middleware/*"], "@utils/*": ["./utils/*"], "@wechat-doodle/shared": ["../../packages/shared/src/index"], "@wechat-doodle/shared/*": ["../../packages/shared/src/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "tests"]}