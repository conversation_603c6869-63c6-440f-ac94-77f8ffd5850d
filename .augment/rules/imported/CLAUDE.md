---
type: "always_apply"
---

# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a WeChat Doodle Jump Clone - a social-first casual gaming experience built as a TypeScript monorepo. The project targets a 1-week MVP timeline with the goal of achieving 32,000 monthly active users and $643K first-year revenue through WeChat ecosystem integration.

## Development Commands

### Primary Development
```bash
# Start all services (client + API)
npm run dev

# Start individual services
npm run dev:client          # Frontend dev server (port 3000)
npm run dev:api            # Backend API server (port 3001)

# Build commands
npm run build              # Build all workspaces
npm run build:client       # Build WeChat miniGame client only
npm run build:api         # Build backend API only
```

### Testing
```bash
# Run all tests
npm test

# Run tests for specific workspace
npm run test --workspace=apps/client
npm run test --workspace=apps/api

# Run client tests with watch mode
cd apps/client && npm run test:watch

# Run single test file
cd apps/client && npm test -- --testPathPattern=gameplay-logic.test.ts
```

### Code Quality
```bash
# Type checking
npm run typecheck          # All workspaces
cd apps/client && npm run typecheck  # Client only

# Linting
npm run lint              # All workspaces
cd apps/client && npm run lint      # Client only

# Format code
npm run format            # All files with Prettier
```

### WeChat Development
```bash
# Start WeChat development server
cd apps/client && npm run dev

# Import project in WeChat Developer Tools:
# - Project Directory: apps/client/
# - AppID: wx3587a6c5a3c4f177
# - Project Type: MiniGame
```

## Architecture Overview

### Monorepo Structure
- **apps/client**: WeChat MiniGame frontend (Phaser 3 + TypeScript)
- **apps/api**: Node.js Express backend with Socket.io for real-time features
- **packages/shared**: Shared TypeScript types and utilities across frontend/backend
- **packages/config**: Shared configuration files (ESLint, TypeScript, Jest)

### Game Architecture (apps/client)

**Core Game Loop**: The game follows a entity-component-system pattern built on Phaser 3:
- `GameScene`: Main game loop, collision detection, camera management
- `Player`: Physics-based player entity with state machine (Idle → Jumping → Falling → Landing → Dead)
- `Platform`: 5 platform types with object pooling (Normal, Spring, Moving, Breaking, Disappearing)
- `PlatformGenerator`: Procedural platform generation with difficulty scaling

**Physics System**: 
- Gravity: 980px/s², Jump Velocity: -450px/s
- Uses Phaser Arcade Physics with Canvas2D renderer for WeChat compatibility
- Object pooling for performance (30 platforms max, reduced for WeChat memory limits)

**WeChat Integration**:
- `WeChatLifecycleManager`: Handles wx.onShow/onHide, memory warnings, audio interruption
- Memory management with 50MB warning threshold, 128MB hard limit
- Touch controls with 0.8x sensitivity for WeChat WebView
- Bundle size optimization for 20MB WeChat limit

### State Management
- Player states: `PlayerState` enum (IDLE, JUMPING, FALLING, LANDING, DEAD)
- Game states: `GameState` interface manages score, level, powerups
- Platform states: Each platform type has specific collision behaviors

### Key Configuration Files
- `apps/client/game.json`: WeChat miniGame configuration
- `apps/client/project.config.json`: WeChat Developer Tools settings
- `apps/client/webpack.config.js`: Build optimization for WeChat constraints
- `packages/shared/src/types/game.ts`: Core game type definitions

## Known Issues & Workarounds

### TypeScript Compilation Errors (25 errors, non-blocking)
- **Player.setState conflict**: Player class `setState` method conflicts with Phaser Sprite base class
  - Workaround: Rename to `setPlayerState` to avoid collision
- **Type definition mismatches**: Some Phaser 3 API incompatibilities with TypeScript strict mode
- **WeChat type definitions**: Some wx.* APIs lack complete type definitions

### WeChat Platform Constraints
- Canvas2D renderer required (WebGL not supported in WeChat WebView)
- Memory limit: 128MB hard limit, 50MB warning threshold
- Touch sensitivity: 0.8x multiplier required for WeChat WebView touch handling
- Bundle size: 20MB total limit, use subpackages for larger games

## Performance Targets
- **FPS**: 60fps target with graceful degradation to 30fps
- **Load Time**: <3s on 3G networks
- **Bundle Size**: Currently 21.3MB (needs optimization for <20MB WeChat limit)
- **Memory Usage**: <50MB warning threshold, monitor with performance overlay

## Testing Strategy
- Logic tests: 23 comprehensive tests covering physics constants, game balance, WeChat optimizations
- All tests passing with 100% core logic coverage
- Jest + jsdom + canvas-mock for game entity testing
- WeChat API mocking in `tests/setup.ts`