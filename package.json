{"name": "wechat-minigame-doodle-jump", "version": "1.0.0", "description": "WeChat Doodle Jump Clone - Social-First Casual Gaming Experience", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "npm run dev:api & npm run dev:client", "dev:client": "npm run dev --workspace=apps/client", "dev:api": "npm run dev --workspace=apps/api", "build": "npm run build --workspaces", "build:client": "npm run build --workspace=apps/client", "build:api": "npm run build --workspace=apps/api", "test": "npm run test --workspaces", "lint": "eslint . --ext .ts,.tsx,.js,.jsx", "typecheck": "npm run typecheck --workspaces", "clean": "rm -rf node_modules apps/*/node_modules packages/*/node_modules", "install:all": "npm install", "format": "prettier --write .", "prepare": "husky install"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "husky": "^8.0.3", "lint-staged": "^15.2.0", "prettier": "^3.2.4", "typescript": "^5.3.3"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,yaml,yml}": ["prettier --write"]}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/username/wechat-minigame-doodle-jump.git"}, "keywords": ["wechat", "minigame", "doodle-jump", "phaser", "social-gaming", "casual-game"], "author": "Your Name", "license": "MIT"}