# WeChat Doodle Jump Clone

> Social-First Casual Gaming Experience for WeChat Platform

一个专为微信生态系统设计的跳跳乐游戏，融合社交功能和休闲游戏体验。

## 🎮 项目概述

**WeChat Doodle Jump Clone** 是一个基于经典 Doodle Jump 玩法的社交休闲游戏，专门针对中国微信用户群体优化。游戏采用"社交优先"的设计理念，通过好友排行榜、成就分享、直接挑战等功能，创造病毒式传播和长期用户粘性。

### 核心特性

- 🚀 **经典跳跃玩法**: 基于 Phaser 3 引擎，60fps 流畅体验
- 👥 **社交集成**: 微信好友排行榜、成就分享、直接挑战
- 🎁 **每日奖励**: 7天递增奖励系统，提升用户留存
- 💰 **虚拟经济**: 金币系统、皮肤商店、微信支付集成
- 🏆 **成就系统**: 多维度成就解锁，增强游戏深度
- 📱 **微信原生**: 完全集成微信 SDK，支持一键登录和分享

## 🏗️ 技术架构

### 前端 (WeChat MiniGame)
- **游戏引擎**: Phaser 3.70+ (HTML5 游戏引擎)
- **开发语言**: TypeScript 5.3+
- **构建工具**: Webpack 5 (优化至 20MB 限制)
- **状态管理**: 原生 + 共享状态模式
- **网络通信**: Axios + Socket.io Client

### 后端 (Node.js API)
- **运行环境**: Node.js 18+ 
- **Web 框架**: Express.js 4.18+
- **数据库**: MongoDB + Mongoose ODM
- **缓存**: Redis (排行榜和会话管理)
- **实时通信**: Socket.io
- **认证**: JWT + 微信 OAuth

### 基础设施
- **云平台**: 阿里云 (中国合规)
- **CDN**: 阿里云 CDN (资源分发)
- **监控**: 自建监控 + 阿里云云监控
- **CI/CD**: GitHub Actions
- **IaC**: Terraform

## 📁 项目结构

```
wechat-minigame-doodle-jump/
├── apps/                           # 应用程序
│   ├── client/                     # 微信小游戏前端
│   │   ├── src/
│   │   │   ├── components/         # UI 组件
│   │   │   ├── game/              # 游戏逻辑
│   │   │   ├── services/          # API 服务
│   │   │   ├── stores/            # 状态管理
│   │   │   └── utils/             # 工具函数
│   │   ├── public/                # 静态资源
│   │   ├── game.json              # 微信小游戏配置
│   │   └── project.config.json    # 项目配置
│   └── api/                       # 后端 API 服务
│       ├── src/
│       │   ├── routes/            # 路由控制器
│       │   ├── services/          # 业务逻辑
│       │   ├── models/            # 数据模型
│       │   ├── middleware/        # 中间件
│       │   └── utils/             # 工具函数
│       └── tests/                 # 测试文件
├── packages/                      # 共享包
│   ├── shared/                    # 共享类型和工具
│   │   ├── src/
│   │   │   ├── types/            # TypeScript 类型定义
│   │   │   ├── constants/        # 常量定义
│   │   │   └── utils/            # 共享工具函数
│   │   └── package.json
│   └── config/                    # 配置文件
│       ├── eslint/               # ESLint 配置
│       ├── typescript/           # TypeScript 配置
│       └── jest/                 # Jest 配置
├── infrastructure/                # 基础设施即代码
│   └── terraform/                # Terraform 配置
├── scripts/                      # 构建和部署脚本
├── docs/                         # 项目文档
│   ├── prd.md                   # 产品需求文档
│   ├── architecture.md          # 技术架构文档
│   └── api.md                   # API 文档
├── .github/                      # GitHub Actions
│   └── workflows/
│       └── ci.yml               # CI/CD 流水线
├── package.json                  # 根项目配置
├── tsconfig.json                # TypeScript 配置
├── .env.example                 # 环境变量模板
└── README.md                    # 项目说明
```

## 🚀 快速开始

### 开发环境要求

- Node.js 18.0+
- npm 9.0+
- MongoDB 6.0+
- Redis 7.0+
- 微信开发者工具

### 本地开发设置

1. **克隆项目**
   ```bash
   git clone https://github.com/username/wechat-minigame-doodle-jump.git
   cd wechat-minigame-doodle-jump
   ```

2. **安装依赖**
   ```bash
   npm install
   ```

3. **环境配置**
   ```bash
   cp .env.example .env
   # 编辑 .env 文件，填入必要的配置信息
   ```

4. **启动数据库服务**
   ```bash
   # 启动 MongoDB
   mongod --dbpath ./data/db
   
   # 启动 Redis
   redis-server
   ```

5. **运行开发服务器**
   ```bash
   # 启动所有服务 (API + Client)
   npm run dev
   
   # 或分别启动
   npm run dev:api      # 后端 API (端口 3001)
   npm run dev:client   # 前端开发服务器 (端口 3000)
   ```

6. **微信开发者工具**
   - 打开微信开发者工具
   - 导入项目目录: `apps/client/`
   - 设置 AppID (测试号或正式号)

### 构建和部署

```bash
# 构建生产版本
npm run build

# 运行测试
npm test

# 代码检查
npm run lint

# 类型检查
npm run typecheck
```

## 🎯 核心功能

### 1. 游戏玩法
- **经典跳跃**: 重力物理、平台生成、得分系统
- **道具系统**: 双倍跳跃、护盾、磁力、加速等
- **平台类型**: 普通、弹簧、移动、破碎、冰面等
- **无限模式**: 程序化生成，难度递增

### 2. 社交功能
- **好友排行榜**: 实时更新，周榜/月榜
- **成就分享**: 一键分享到微信朋友圈
- **直接挑战**: 向好友发起分数挑战
- **社交礼品**: 节日礼品赠送系统

### 3. 用户系统
- **微信登录**: 无缝集成微信 OAuth
- **个人资料**: 头像、昵称、等级、徽章
- **好友管理**: 导入微信好友，添加游戏好友
- **隐私设置**: 可控的信息可见性

### 4. 激励系统
- **每日奖励**: 7天递增奖励日历
- **成就系统**: 多类型成就解锁
- **等级系统**: 经验值升级机制
- **虚拟货币**: 金币赚取和消费

### 5. 商业化
- **虚拟商品**: 角色皮肤、道具包
- **微信支付**: 安全便捷的支付体验
- **价格策略**: ¥1-30 多档位选择
- **转化漏斗**: 免费试玩到付费转化

## 📊 性能指标

### 游戏性能
- **帧率**: 60fps 稳定运行
- **加载时间**: <3秒 (3G网络)
- **包体大小**: <20MB (微信限制)
- **内存使用**: <100MB (中端设备)

### 业务指标
- **目标用户**: 32,000 月活跃用户
- **留存率**: 15% (7日留存)
- **转化率**: 8% (付费转化)
- **病毒系数**: 0.3 (社交传播)

## 🔒 合规和安全

### 中国法规合规
- **ICP 备案**: 网站运营许可
- **游戏版号**: 商业化运营许可
- **防沉迷**: 未成年人保护机制
- **数据安全**: 个人信息保护法合规

### 技术安全
- **数据加密**: HTTPS + 数据库加密
- **访问控制**: JWT 认证 + RBAC
- **支付安全**: 微信支付安全保障
- **API 安全**: 频率限制 + 输入验证

## 📈 数据监控

### 关键指标
- **用户行为**: 游戏时长、点击热图、留存漏斗
- **性能监控**: 响应时间、错误率、资源使用
- **业务指标**: DAU/MAU、ARPU、LTV
- **社交数据**: 分享率、好友邀请转化

### 监控工具
- **实时监控**: 自建仪表板
- **错误追踪**: 集成错误收集
- **性能分析**: 前端性能监控
- **业务分析**: 用户行为分析

## 🤝 贡献指南

### 开发流程
1. Fork 项目到个人仓库
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

### 代码规范
- **TypeScript**: 严格模式，完整类型定义
- **ESLint**: 统一代码风格检查
- **Prettier**: 自动代码格式化
- **Husky**: Git hooks 自动化检查

### 测试要求
- **单元测试**: ≥80% 代码覆盖率
- **集成测试**: API 端点全覆盖
- **E2E 测试**: 关键用户流程
- **性能测试**: 负载和压力测试

## 📄 许可证

本项目采用 MIT 许可证。详细信息请参阅 [LICENSE](LICENSE) 文件。

## 🙋‍♂️ 支持和反馈

- **问题反馈**: [GitHub Issues](https://github.com/username/wechat-minigame-doodle-jump/issues)
- **功能建议**: [GitHub Discussions](https://github.com/username/wechat-minigame-doodle-jump/discussions)
- **技术交流**: 微信群 (扫码入群)
- **商业合作**: <EMAIL>

---

**让我们一起打造最有趣的微信社交游戏！** 🎮✨