# Story 1.3: Core Jump Mechanics

## Status
Draft

## Story

**As a** casual gamer,  
**I want** to play classic Doodle Jump with smooth jumping mechanics and intuitive controls,  
**so that** I can enjoy familiar, engaging gameplay during my short break periods.

## Acceptance Criteria

1. **Character Physics**: Smooth vertical jumping character with realistic physics, gravity, and collision detection working at 60fps target.

2. **Platform Generation**: Procedural platform generation creating challenging but fair jumping sequences with appropriate spacing and variety.

3. **Touch Controls**: Responsive touch or tilt controls optimized for one-handed mobile gameplay with immediate feedback and intuitive interaction.

4. **Game Boundaries**: Proper game boundaries, falling detection, and game over states with clear visual feedback and restart options.

5. **Performance**: Consistent 60fps performance on target devices (Xiaomi Redmi Note series) with smooth animations and responsive controls.

## Tasks / Subtasks

- [ ] **Task 1**: Setup Phaser 3 Game Scene and Physics System (AC: 1, 5)
  - [ ] Create GameScene class extending Phaser.Scene
  - [ ] Initialize Phaser 3 physics system with Arcade Physics
  - [ ] Configure game world bounds and physics settings
  - [ ] Setup 60fps target performance configuration
  - [ ] Implement scene lifecycle methods (preload, create, update)

- [ ] **Task 2**: Implement Player Character Physics (AC: 1)
  - [ ] Create Player class with position, velocity, and state properties
  - [ ] Implement gravity system using PHYSICS.GRAVITY constant (980 px/s²)
  - [ ] Add jump mechanics with PHYSICS.JUMP_VELOCITY (-450 px/s)
  - [ ] Configure horizontal movement with PHYSICS.HORIZONTAL_SPEED (200 px/s)
  - [ ] Add physics bounds checking and collision detection
  - [ ] Implement player state management (idle, jumping, falling, landing, dead)

- [ ] **Task 3**: Create Platform Generation System (AC: 2)
  - [ ] Implement Platform class with position, type, and collision properties
  - [ ] Create procedural platform generation algorithm
  - [ ] Configure platform spacing using PLATFORMS constants (MIN_GAP: 30px, MAX_GAP: 120px)
  - [ ] Implement platform recycling system for performance
  - [ ] Add platform variety types (normal, spring, moving, breaking)
  - [ ] Implement collision detection between player and platforms

- [ ] **Task 4**: Implement Touch and Tilt Controls (AC: 3)
  - [ ] Create InputHandler class for touch and tilt input processing
  - [ ] Implement touch controls for horizontal movement
  - [ ] Add tilt control support using device orientation API
  - [ ] Configure one-handed control optimization
  - [ ] Add haptic feedback for touch interactions
  - [ ] Implement input smoothing and acceleration

- [ ] **Task 5**: Setup Game Boundaries and Game Over States (AC: 4)
  - [ ] Implement world boundaries and falling detection
  - [ ] Create game over trigger when player falls below screen
  - [ ] Add game state management (playing, paused, game over)
  - [ ] Implement restart functionality
  - [ ] Add visual feedback for game boundaries
  - [ ] Create game over screen UI components

- [ ] **Task 6**: Performance Optimization and Testing (AC: 5)
  - [ ] Implement object pooling for platforms and particles
  - [ ] Optimize rendering with efficient sprite batching
  - [ ] Add performance monitoring and FPS display
  - [ ] Test on target devices (Xiaomi Redmi Note series)
  - [ ] Profile memory usage and optimize garbage collection
  - [ ] Implement smooth animations with requestAnimationFrame

## Dev Notes

### Previous Story Insights
No previous stories completed - this is the first gameplay implementation story.

### Game Physics Configuration
[Source: packages/shared/src/constants/index.ts#GAME_CONFIG]
- **Gravity**: 980 pixels per second squared
- **Jump Velocity**: -450 pixels per second (negative for upward movement)
- **Horizontal Speed**: 200 pixels per second
- **Max Fall Speed**: 800 pixels per second
- **Bounce Damping**: 0.8 for platform bounces
- **Friction**: 0.95 for movement damping

**Player Configuration**:
- **Size**: 32x32 pixels
- **Max Horizontal Speed**: 300 pixels per second
- **Acceleration**: 500 pixels per second squared

**Platform Configuration**:
- **Base Width**: 80 pixels
- **Min Gap**: 30 pixels between platforms
- **Max Gap**: 120 pixels between platforms
- **Special Platform Chance**: 15% (0.15)
- **Power Up Chance**: 8% (0.08)

**Camera Configuration**:
- **Smoothing**: 0.1 for smooth camera following
- **Offset Y**: 200 pixels offset for optimal view

### Data Models and Types
[Source: packages/shared/src/types/game.ts]

**Game State Interface**:
```typescript
interface GameState {
  isPlaying: boolean;
  isPaused: boolean;
  score: number;
  platforms: Platform[];
  player: Player;
  powerUps: ActivePowerUp[];
  camera: Camera;
  gameTime: number;
}
```

**Player Data Interface**:
```typescript
interface PlayerData {
  id: string;
  position: { x: number; y: number };
  velocity: { x: number; y: number };
  state: PlayerState; // IDLE, JUMPING, FALLING, LANDING, DEAD
  skin: PlayerSkin;
  animations: { current: string; frame: number; speed: number };
  effects: ActiveEffect[];
}
```

**Platform Interface**:
```typescript  
interface Platform {
  id: string;
  x: number;
  y: number;
  width: number;
  type: PlatformType; // NORMAL, BREAKING, MOVING, SPRING, DISAPPEARING
  isMoving?: boolean;
  movementSpeed?: number;
  movementRange?: number;
}
```

**Game Physics Interface**:
```typescript
interface GamePhysics {
  gravity: number;
  jumpForce: number; 
  horizontalSpeed: number;
  maxFallSpeed: number;
  friction: number;
  bounce: number;
  platformCollision: {
    width: number;
    height: number;
    offset: { x: number; y: number };
  };
}
```

### Component Architecture
[Source: docs/architecture.md#Components]

**Game Engine Component Structure**:
- **GameStateManager**: Manages game state transitions, pause/resume, game over scenarios
- **ScoreManager**: Handles score calculation, achievement triggers, and personal best detection  
- **InputHandler**: Processes touch/tilt controls optimized for WeChat WebView
- **AssetLoader**: Manages game assets with progressive loading and 20MB size optimization

### File Locations and Project Structure
[Source: docs/architecture.md#unified-project-structure]

**Client Game Files Location**:
```
apps/client/src/game/
├── scenes/
│   └── GameScene.ts          # Main game scene
├── entities/
│   ├── Player.ts             # Player character class
│   └── Platform.ts           # Platform class
├── systems/
│   ├── PhysicsSystem.ts      # Physics management
│   ├── InputSystem.ts        # Input handling
│   └── PlatformGenerator.ts  # Platform generation
├── managers/
│   ├── GameStateManager.ts   # Game state management
│   └── AssetManager.ts       # Asset loading
└── config/
    └── GameConfig.ts         # Game configuration
```

### Technical Constraints
[Source: docs/architecture.md#tech-stack]
- **Game Engine**: Phaser 3.70+ with TypeScript for type safety
- **Performance Target**: 60fps on Xiaomi Redmi Note series (mid-range Android)
- **Bundle Size**: Must stay under 20MB WeChat limit
- **WebView Compatibility**: Must work in WeChat WebView environment
- **Touch Optimization**: One-handed mobile gameplay priority

### WeChat Integration Requirements
[Source: docs/architecture.md#architectural-patterns]
- Game must be compatible with WeChat WebView
- Support for WeChat canvas context API
- Integration with WeChat touch and device orientation APIs
- Proper handling of WeChat app lifecycle events (pause/resume)

### Performance Optimization Requirements
[Source: docs/architecture.md#components]
- Object pooling for frequently created/destroyed objects
- Efficient sprite batching for rendering performance
- Memory management and garbage collection optimization
- Asset compression and progressive loading
- Frame rate monitoring and performance profiling

## Testing

### Test File Location
[Source: .bmad-core/core-config.yaml]
Tests should be placed in: `apps/client/tests/game/` following the source directory structure.

### Testing Framework and Standards
[Source: docs/architecture.md#tech-stack]
- **Framework**: Jest 29+ with Phaser Testing utilities
- **Test Types**: Unit tests for game logic, integration tests for physics system
- **Coverage Target**: ≥80% code coverage for game mechanics
- **Performance Tests**: Frame rate and memory usage validation

### Specific Testing Requirements for This Story
- **Physics Tests**: Verify gravity, jump velocity, and collision detection accuracy
- **Platform Generation Tests**: Validate proper spacing, variety, and recycling
- **Input Tests**: Test touch and tilt control responsiveness and accuracy  
- **Performance Tests**: Verify 60fps performance on target devices
- **Game State Tests**: Test proper game over detection and restart functionality
- **Boundary Tests**: Verify game boundaries and falling detection work correctly

### Test Files to Create
```
apps/client/tests/game/
├── scenes/
│   └── GameScene.test.ts
├── entities/
│   ├── Player.test.ts
│   └── Platform.test.ts
├── systems/
│   ├── PhysicsSystem.test.ts
│   ├── InputSystem.test.ts
│   └── PlatformGenerator.test.ts
└── integration/
    └── gameplay.integration.test.ts
```

## Change Log

| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2024-08-05 | 1.0 | Initial story creation for core jump mechanics implementation | Bob (SM) |

## Dev Agent Record

_This section will be populated by the development agent during implementation._

### Agent Model Used
_To be filled by dev agent_

### Debug Log References  
_To be filled by dev agent_

### Completion Notes List
_To be filled by dev agent_

### File List
_To be filled by dev agent_

## QA Results

_This section will be populated by the QA Agent after story implementation review._