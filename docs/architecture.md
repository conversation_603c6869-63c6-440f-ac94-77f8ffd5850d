# WeChat Doodle Jump Clone Fullstack Architecture Document

## Introduction

This document outlines the complete fullstack architecture for WeChat Doodle Jump Clone, including backend systems, frontend implementation, and their integration. It serves as the single source of truth for AI-driven development, ensuring consistency across the entire technology stack.

This unified approach combines what would traditionally be separate backend and frontend architecture documents, streamlining the development process for modern fullstack applications where these concerns are increasingly intertwined.

### Starter Template or Existing Project

**Assessment**: This is a greenfield WeChat miniGame project with specific platform constraints.

**Recommendation**: Given the 1-week MVP timeline and WeChat platform requirements, I recommend starting with:
- Phaser 3 TypeScript starter template for rapid game development
- WeChat miniGame official template from WeChat Developer Tools
- Node.js Express TypeScript starter for backend API structure

**Constraints Imposed**: 
- WeChat WebView runtime environment limits certain browser APIs
- 20MB total package size limit for WeChat miniGame
- Mandatory WeChat SDK integration for authentication and social features
- China deployment requirements (ICP license, Alibaba Cloud hosting)

**Decision**: Proceed with custom setup combining proven templates rather than complex fullstack starter to maintain control over WeChat-specific optimizations.

## Change Log

| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2024-12-05 | 1.0 | Initial architecture document creation for WeChat Doodle Jump clone | Architect |

# High Level Architecture

## Technical Summary

This WeChat Doodle Jump clone employs a modern fullstack architecture optimized for rapid development and WeChat ecosystem integration. The system uses a monorepo structure with Phaser 3.70+ TypeScript frontend delivering 60fps gameplay within WeChat WebView constraints, paired with a Node.js Express backend providing real-time social features through Socket.io integration. The architecture prioritizes WeChat-native authentication, social sharing, and payment processing through dedicated SDK integration layers, while maintaining scalable microservices organization within the monorepo for user management, game logic, and social features. Infrastructure deployment targets Alibaba Cloud for China compliance with auto-scaling capabilities, CDN asset delivery, and comprehensive monitoring to support viral growth scenarios and the aggressive 1-week MVP timeline through proven technology choices and automated deployment pipelines.

## Platform and Infrastructure Choice

Based on the PRD requirements for China market compliance, WeChat ecosystem integration, and aggressive 1-week timeline, I'm evaluating these platform options:

**Option 1: Alibaba Cloud Full Stack (Recommended)**
- **Pros**: China compliance built-in, excellent performance for Chinese users, comprehensive WeChat SDK support, auto-scaling, CDN integration
- **Cons**: Learning curve for non-Chinese developers, vendor lock-in
- **Fit**: Perfect for WeChat miniGame with ICP compliance and local performance

**Option 2: Tencent Cloud (WeChat Native)**
- **Pros**: Direct WeChat integration, optimal for WeChat ecosystem, native payment processing
- **Cons**: Limited global reach, smaller developer community
- **Fit**: Excellent WeChat integration but potentially slower development

**Option 3: AWS China with WeChat Bridge**
- **Pros**: Familiar AWS tooling, global best practices, strong monitoring
- **Cons**: WeChat integration complexity, compliance overhead, higher setup time
- **Fit**: Good for experienced AWS teams but adds complexity

**Recommendation: Alibaba Cloud Full Stack**

The aggressive 1-week timeline and China-first strategy make Alibaba Cloud the optimal choice. Native WeChat SDK support, built-in ICP compliance, and excellent performance for Chinese users outweigh the learning curve. The integrated CDN and auto-scaling features directly support our viral growth requirements.

**Platform:** Alibaba Cloud
**Key Services:** ECS (Elastic Compute Service), ApsaraDB for MongoDB, ApsaraDB for Redis, CDN, SLB (Server Load Balancer), WeChat SDK integration
**Deployment Host and Regions:** China East 1 (Hangzhou) primary, China North 2 (Beijing) secondary for redundancy

## Repository Structure

For this WeChat miniGame project, a monorepo approach is optimal given the 1-week timeline, shared TypeScript interfaces between client and server, and need for coordinated deployment of game client and backend services.

**Structure:** Monorepo with clear client/server separation
**Monorepo Tool:** npm workspaces (lightweight, no additional tooling overhead for MVP)
**Package Organization:** Client (WeChat miniGame), Server (Node.js APIs), Shared (TypeScript interfaces and utilities)

The monorepo enables rapid development with shared code reuse, simplified dependency management, and coordinated versioning across the fullstack, while maintaining clear boundaries between frontend game logic and backend services.

## High Level Architecture Diagram

```mermaid
graph TB
    subgraph "WeChat Ecosystem"
        WU[WeChat Users]
        WMP[WeChat MiniProgram Platform]
        WS[WeChat SDK APIs]
        WP[WeChat Pay]
    end
    
    subgraph "Client Layer"
        GC[Game Client - Phaser]
        WI[WeChat Integration Layer]
        GU[Game UI Components]
    end
    
    subgraph "API Gateway"
        AG[API Gateway/Load Balancer]
    end
    
    subgraph "Backend Services"
        AS[Auth Service]
        GS[Game Service]
        SS[Social Service]
        PS[Payment Service]
        NS[Notification Service]
    end
    
    subgraph "Data Layer"
        MDB[(MongoDB)]
        RDS[(Redis Cache)]
        FS[File Storage]
    end
    
    subgraph "External Services"
        CDN[Alibaba CDN]
        MON[Monitoring/Analytics]
    end
    
    WU --> WMP
    WMP --> GC
    GC --> WI
    WI --> WS
    WI --> WP
    GC --> GU
    
    GC --> AG
    AG --> AS
    AG --> GS
    AG --> SS
    AG --> PS
    AG --> NS
    
    AS --> MDB
    GS --> MDB
    SS --> MDB
    PS --> MDB
    
    GS --> RDS
    SS --> RDS
    
    NS --> RDS
    
    CDN --> GC
    MON --> AS
    MON --> GS
    MON --> SS
    MON --> PS
```

## Architectural Patterns

- **WeChat MiniGame Architecture:** Client-side game engine with server-side social and persistence services - *Rationale:* Optimal performance for gameplay while enabling rich social features and data persistence
- **Microservices within Monorepo:** Separate services for auth, game, social, and payments with shared deployment - *Rationale:* Enables focused development and scaling while maintaining deployment simplicity for MVP
- **API Gateway Pattern:** Single entry point for all backend services with routing and middleware - *Rationale:* Centralized authentication, rate limiting, and monitoring for WeChat SDK integration
- **Repository Pattern:** Abstract data access layer for MongoDB operations - *Rationale:* Enables testing and future database optimization without business logic changes
- **Event-Driven Social Features:** Real-time social interactions through Socket.io with Redis pub/sub - *Rationale:* Immediate social feedback essential for viral engagement and user retention
- **Component-Based Game Architecture:** Phaser 3 scene management with reusable game components - *Rationale:* Maintainable game code with clear separation between gameplay, UI, and social features
- **Circuit Breaker Pattern:** Resilient WeChat API integration with fallback mechanisms - *Rationale:* Ensures game functionality continues even with WeChat service intermittency

# Tech Stack

This is the DEFINITIVE technology selection for the entire project. All development must use these exact versions and technologies.

## Technology Stack Table

| Category | Technology | Version | Purpose | Rationale |
|----------|------------|---------|---------|-----------|
| Frontend Language | TypeScript | 5.0+ | Type-safe game development | Essential for large codebase maintainability and WeChat SDK integration |
| Frontend Framework | Phaser | 3.70+ | HTML5 game engine | Proven performance in WeChat WebView, extensive documentation, 60fps capability |
| UI Component Library | Custom Components | - | WeChat-styled UI elements | No external UI library needed - custom components for WeChat design consistency |
| State Management | Phaser Data Manager + Redux Toolkit | 1.9+ | Game state and UI state | Phaser built-in for game state, Redux for persistent user/social data |
| Backend Language | Node.js | 18+ LTS | Server runtime | Excellent WeChat SDK support, rapid development, shared TypeScript codebase |
| Backend Framework | Express | 4.18+ | Web framework | Mature, lightweight, extensive middleware ecosystem for rapid development |
| API Style | REST + Socket.io | - | API communication | REST for standard operations, Socket.io for real-time social features |
| Database | MongoDB | 6.0+ | Primary data store | Flexible schema for user profiles, scores, social data, excellent Node.js integration |
| Cache | Redis | 7.0+ | Caching and sessions | Essential for leaderboards, session management, real-time features performance |
| File Storage | Alibaba Cloud OSS | - | Asset and image storage | CDN integration, China compliance, cost-effective for game assets |
| Authentication | WeChat SDK + JWT | - | User authentication | Native WeChat integration required, JWT for API session management |
| Frontend Testing | Jest + Phaser Testing | 29+ | Unit and integration tests | Standard JavaScript testing, Phaser-specific testing utilities |
| Backend Testing | Jest + Supertest | 29+ | API and service tests | Comprehensive testing framework with API testing capabilities |
| E2E Testing | Playwright | 1.40+ | End-to-end testing | Cross-browser testing, WeChat WebView simulation capabilities |
| Build Tool | Webpack | 5.0+ | Module bundling | Required for Phaser 3, WeChat miniGame compatibility, asset optimization |
| Bundler | Webpack | 5.0+ | Code bundling | Integrated with build tool, optimized for 20MB WeChat size limit |
| IaC Tool | Terraform | 1.5+ | Infrastructure as code | Alibaba Cloud provider support, version control for infrastructure |
| CI/CD | GitHub Actions | - | Build and deployment | Free tier suitable for MVP, excellent integration with development workflow |
| Monitoring | Alibaba Cloud ARMS | - | Application monitoring | Native cloud integration, real-time performance monitoring |
| Logging | Winston + Alibaba Log Service | 3.8+ | Structured logging | Professional logging framework with cloud integration |
| CSS Framework | Tailwind CSS | 3.3+ | Utility-first styling | Rapid UI development, small bundle size, customizable for WeChat aesthetics |

# Data Models

Based on the PRD requirements, I've identified the core business entities needed for the WeChat Doodle Jump clone. These models support user management, gameplay, social features, and monetization while maintaining clear relationships and efficient data access patterns.

## User

**Purpose:** Central user entity managing WeChat authentication, profile data, and game statistics for social features and personalization.

**Key Attributes:**
- id: ObjectId - Unique database identifier
- wechatId: string - WeChat user unique identifier from SDK
- nickname: string - WeChat display name for social features
- avatar: string - WeChat avatar URL for leaderboards and social UI
- totalScore: number - Lifetime cumulative score for achievements
- highScore: number - Personal best score for leaderboards
- gamesPlayed: number - Total games for engagement metrics
- coins: number - Virtual currency balance for purchases
- createdAt: Date - Registration timestamp
- lastActiveAt: Date - Last activity for retention tracking
- dailyStreak: number - Consecutive daily login streak
- lastStreakDate: Date - Last daily reward claim date

### TypeScript Interface

```typescript
interface User {
  id: string;
  wechatId: string;
  nickname: string;
  avatar: string;
  totalScore: number;
  highScore: number;
  gamesPlayed: number;
  coins: number;
  createdAt: Date;
  lastActiveAt: Date;
  dailyStreak: number;
  lastStreakDate: Date;
}
```

### Relationships
- One-to-many with GameSession (user's game history)
- One-to-many with Achievement (user's earned achievements)
- Many-to-many with User through Friendship (social connections)
- One-to-many with Purchase (monetization history)

## GameSession

**Purpose:** Individual game playthrough record enabling score tracking, leaderboards, social sharing, and gameplay analytics.

**Key Attributes:**
- id: ObjectId - Unique session identifier
- userId: ObjectId - Reference to User who played
- score: number - Final score achieved in session
- duration: number - Game session length in seconds
- platformsJumped: number - Number of platforms reached
- powerupsUsed: string[] - Array of powerup types used
- createdAt: Date - Game completion timestamp
- shared: boolean - Whether score was shared to WeChat
- challengeId: ObjectId - Reference to Challenge if applicable

### TypeScript Interface

```typescript
interface GameSession {
  id: string;
  userId: string;
  score: number;
  duration: number;
  platformsJumped: number;
  powerupsUsed: string[];
  createdAt: Date;
  shared: boolean;
  challengeId?: string;
}
```

### Relationships
- Many-to-one with User (player who completed session)
- Many-to-one with Challenge (if session was challenge response)

## Achievement

**Purpose:** Gamification system tracking user milestones, social interactions, and progression to drive engagement and sharing.

**Key Attributes:**
- id: ObjectId - Unique achievement identifier
- userId: ObjectId - Reference to User who earned achievement
- type: string - Achievement category (score, social, streak, special)
- name: string - Display name for UI and sharing
- description: string - Achievement details for user understanding
- iconUrl: string - Visual representation for UI consistency
- earnedAt: Date - Timestamp when achievement was unlocked
- shared: boolean - Whether achievement was shared to WeChat
- rarity: string - Common/Rare/Epic for visual distinction

### TypeScript Interface

```typescript
interface Achievement {
  id: string;
  userId: string;
  type: 'score' | 'social' | 'streak' | 'special';
  name: string;
  description: string;
  iconUrl: string;
  earnedAt: Date;
  shared: boolean;
  rarity: 'common' | 'rare' | 'epic';
}
```

### Relationships
- Many-to-one with User (achievement owner)

## Friendship

**Purpose:** Social relationship management enabling friend leaderboards, challenges, and social features core to viral distribution.

**Key Attributes:**
- id: ObjectId - Unique friendship identifier
- userId: ObjectId - Reference to first user
- friendId: ObjectId - Reference to second user (WeChat friend)
- status: string - Relationship status for privacy controls
- createdAt: Date - Friendship establishment date
- lastInteractionAt: Date - Most recent social interaction timestamp

### TypeScript Interface

```typescript
interface Friendship {
  id: string;
  userId: string;
  friendId: string;
  status: 'active' | 'blocked' | 'pending';
  createdAt: Date;
  lastInteractionAt: Date;
}
```

### Relationships
- Many-to-one with User (both userId and friendId reference User)

## Challenge

**Purpose:** Direct friend competition system enabling targeted social engagement and viral user acquisition through WeChat sharing.

**Key Attributes:**
- id: ObjectId - Unique challenge identifier
- challengerId: ObjectId - User who created the challenge
- targetId: ObjectId - Friend being challenged
- targetScore: number - Score to beat for challenge completion
- message: string - Personal challenge message
- status: string - Challenge lifecycle state
- createdAt: Date - Challenge creation timestamp
- completedAt: Date - Challenge completion timestamp (if applicable)
- responseScore: number - Target user's response score

### TypeScript Interface

```typescript
interface Challenge {
  id: string;
  challengerId: string;
  targetId: string;
  targetScore: number;
  message: string;
  status: 'pending' | 'completed' | 'failed' | 'expired';
  createdAt: Date;
  completedAt?: Date;
  responseScore?: number;
}
```

### Relationships
- Many-to-one with User (challenger and target both reference User)
- One-to-many with GameSession (challenge response sessions)

## Purchase

**Purpose:** WeChat Pay transaction tracking for virtual currency and premium items supporting freemium monetization strategy.

**Key Attributes:**
- id: ObjectId - Unique transaction identifier
- userId: ObjectId - Reference to purchasing user
- itemType: string - Type of purchase (coins, cosmetic, powerup)
- itemId: string - Specific item identifier for inventory
- amount: number - Purchase price in Chinese Yuan
- coins: number - Virtual currency amount (if applicable)
- wechatTransactionId: string - WeChat Pay transaction reference
- status: string - Payment processing status
- createdAt: Date - Purchase initiation timestamp
- completedAt: Date - Payment completion timestamp

### TypeScript Interface

```typescript
interface Purchase {
  id: string;
  userId: string;
  itemType: 'coins' | 'cosmetic' | 'powerup';
  itemId: string;
  amount: number;
  coins?: number;
  wechatTransactionId: string;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  createdAt: Date;
  completedAt?: Date;
}
```

### Relationships
- Many-to-one with User (purchaser)

# API Specification

Based on the REST + Socket.io API style selected in the tech stack, this specification defines all endpoints required for the WeChat Doodle Jump clone functionality including authentication, gameplay, social features, and monetization.

## REST API Specification

```yaml
openapi: 3.0.0
info:
  title: WeChat Doodle Jump Clone API
  version: 1.0.0
  description: RESTful API for WeChat miniGame with social features and monetization
servers:
  - url: https://api.wechat-doodle-jump.com/v1
    description: Production server
  - url: http://localhost:3000/v1  
    description: Development server

paths:
  /auth/wechat:
    post:
      summary: Authenticate user with WeChat credentials
      tags: [Authentication]
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                code:
                  type: string
                  description: WeChat authorization code from SDK
                encryptedData:
                  type: string
                  description: Encrypted user info from WeChat
                iv:
                  type: string
                  description: Initialization vector for decryption
              required: [code]
      responses:
        200:
          description: Authentication successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  token:
                    type: string
                    description: JWT token for API access
                  user:
                    $ref: '#/components/schemas/User'
        401:
          description: Authentication failed

  /users/profile:
    get:
      summary: Get current user profile
      tags: [Users]
      security:
        - bearerAuth: []
      responses:
        200:
          description: User profile retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'

    patch:
      summary: Update user profile
      tags: [Users]
      security:
        - bearerAuth: []
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                nickname:
                  type: string
      responses:
        200:
          description: Profile updated successfully

  /games/sessions:
    post:
      summary: Record completed game session
      tags: [Games]
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                score:
                  type: integer
                  minimum: 0
                duration:
                  type: integer
                  minimum: 1
                platformsJumped:
                  type: integer
                  minimum: 0
                powerupsUsed:
                  type: array
                  items:
                    type: string
                challengeId:
                  type: string
              required: [score, duration, platformsJumped]
      responses:
        201:
          description: Game session recorded
          content:
            application/json:
              schema:
                type: object
                properties:
                  session:
                    $ref: '#/components/schemas/GameSession'
                  newAchievements:
                    type: array
                    items:
                      $ref: '#/components/schemas/Achievement'
                  personalBest:
                    type: boolean

  /leaderboards/friends:
    get:
      summary: Get friend leaderboard
      tags: [Social]
      security:
        - bearerAuth: []
      parameters:
        - name: period
          in: query
          schema:
            type: string
            enum: [weekly, monthly, alltime]
            default: weekly
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
      responses:
        200:
          description: Friend leaderboard retrieved
          content:
            application/json:
              schema:
                type: object
                properties:
                  leaderboard:
                    type: array
                    items:
                      type: object
                      properties:
                        rank:
                          type: integer
                        user:
                          $ref: '#/components/schemas/User'
                        score:
                          type: integer
                        isCurrentUser:
                          type: boolean
                  userRank:
                    type: integer
                    description: Current user's rank in full leaderboard

  /social/friends:
    get:
      summary: Get user's WeChat friends who play the game
      tags: [Social]
      security:
        - bearerAuth: []
      responses:
        200:
          description: Friends list retrieved
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/User'

  /social/challenges:
    get:
      summary: Get user's challenges (sent and received)
      tags: [Social]
      security:
        - bearerAuth: []
      parameters:
        - name: type
          in: query
          schema:
            type: string  
            enum: [sent, received, all]
            default: all
        - name: status
          in: query
          schema:
            type: string
            enum: [pending, completed, failed, expired]
      responses:
        200:
          description: Challenges retrieved
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Challenge'

    post:
      summary: Create new friend challenge
      tags: [Social]
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                targetId:
                  type: string
                  description: Friend's user ID
                targetScore:
                  type: integer
                  minimum: 1
                message:
                  type: string
                  maxLength: 200
              required: [targetId, targetScore]
      responses:
        201:
          description: Challenge created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Challenge'

  /achievements:
    get:
      summary: Get user's achievements
      tags: [Achievements]
      security:
        - bearerAuth: []
      parameters:
        - name: type
          in: query
          schema:
            type: string
            enum: [score, social, streak, special]
      responses:
        200:
          description: Achievements retrieved
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Achievement'

  /achievements/{id}/share:
    post:
      summary: Share achievement to WeChat
      tags: [Achievements]
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        200:
          description: Achievement shared successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  shareData:
                    type: object
                    properties:
                      title:
                        type: string
                      imageUrl:
                        type: string
                      path:
                        type: string

  /store/items:
    get:
      summary: Get available store items
      tags: [Store]
      security:
        - bearerAuth: []
      parameters:
        - name: category  
          in: query
          schema:
            type: string
            enum: [coins, cosmetics, powerups]
      responses:
        200:
          description: Store items retrieved
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    id:
                      type: string
                    name:
                      type: string
                    description:
                      type: string
                    price:
                      type: number
                    currency:
                      type: string
                      enum: [CNY, coins]
                    category:
                      type: string
                    imageUrl:
                      type: string

  /store/purchase:
    post:
      summary: Initiate WeChat Pay purchase
      tags: [Store]
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                itemId:
                  type: string
                quantity:
                  type: integer
                  minimum: 1
                  default: 1
              required: [itemId]
      responses:
        200:
          description: Purchase initiated
          content:
            application/json:
              schema:
                type: object
                properties:
                  paymentData:
                    type: object
                    description: WeChat Pay parameters
                  orderId:
                    type: string

  /store/purchases:
    get:
      summary: Get user's purchase history
      tags: [Store]
      security:
        - bearerAuth: []
      parameters:
        - name: status
          in: query
          schema:
            type: string
            enum: [pending, completed, failed, refunded]
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 50
            default: 20
      responses:
        200:
          description: Purchase history retrieved
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Purchase'

  /rewards/daily:
    get:
      summary: Get daily rewards status
      tags: [Rewards]
      security:
        - bearerAuth: []
      responses:
        200:
          description: Daily rewards status
          content:
            application/json:
              schema:
                type: object
                properties:
                  currentStreak:
                    type: integer
                  canClaim:
                    type: boolean
                  todaysClaimed:
                    type: boolean
                  nextReward:
                    type: object
                    properties:
                      day:
                        type: integer
                      type:
                        type: string
                      amount:
                        type: integer

    post:
      summary: Claim daily reward
      tags: [Rewards]
      security:
        - bearerAuth: []
      responses:
        200:
          description: Daily reward claimed
          content:
            application/json:
              schema:
                type: object
                properties:
                  reward:
                    type: object
                    properties:
                      type:
                        type: string
                      amount:
                        type: integer
                  newStreak:
                    type: integer
        400:
          description: Reward already claimed or not available

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    User:
      type: object
      properties:
        id:
          type: string
        wechatId:
          type: string
        nickname:
          type: string
        avatar:
          type: string
        totalScore:
          type: integer
        highScore:
          type: integer
        gamesPlayed:
          type: integer
        coins:
          type: integer
        dailyStreak:
          type: integer
        createdAt:
          type: string
          format: date-time
        lastActiveAt:
          type: string
          format: date-time

    GameSession:
      type: object
      properties:
        id:
          type: string
        userId:
          type: string
        score:
          type: integer
        duration:
          type: integer
        platformsJumped:
          type: integer
        powerupsUsed:
          type: array
          items:
            type: string
        createdAt:
          type: string
          format: date-time
        shared:
          type: boolean
        challengeId:
          type: string

    Achievement:
      type: object
      properties:
        id:
          type: string
        userId:
          type: string
        type:
          type: string
          enum: [score, social, streak, special]
        name:
          type: string
        description:
          type: string
        iconUrl:
          type: string
        earnedAt:
          type: string
          format: date-time
        shared:
          type: boolean
        rarity:
          type: string
          enum: [common, rare, epic]

    Challenge:
      type: object
      properties:
        id:
          type: string
        challengerId:
          type: string
        targetId:
          type: string
        targetScore:
          type: integer
        message:
          type: string
        status:
          type: string
          enum: [pending, completed, failed, expired]
        createdAt:
          type: string
          format: date-time
        completedAt:
          type: string
          format: date-time
        responseScore:
          type: integer

    Purchase:
      type: object
      properties:
        id:
          type: string
        userId:
          type: string
        itemType:
          type: string
          enum: [coins, cosmetic, powerup]
        itemId:
          type: string
        amount:
          type: number
        coins:
          type: integer
        wechatTransactionId:
          type: string
        status:
          type: string
          enum: [pending, completed, failed, refunded]
        createdAt:
          type: string
          format: date-time
        completedAt:
          type: string
          format: date-time
```

# Components

Based on the architectural patterns, tech stack, and data models, I've identified the major logical components across the fullstack WeChat Doodle Jump clone. These components provide clear boundaries and interfaces while supporting the rapid development timeline and social-first gaming requirements.

## Game Engine Component

**Responsibility:** Core Phaser 3 game engine managing gameplay mechanics, physics, rendering, and user input for the Doodle Jump experience.

**Key Interfaces:**
- GameStateManager: Manages game state transitions, pause/resume, game over scenarios
- ScoreManager: Handles score calculation, achievement triggers, and personal best detection
- InputHandler: Processes touch/tilt controls optimized for WeChat WebView
- AssetLoader: Manages game assets with progressive loading and 20MB size optimization

**Dependencies:** WeChat Integration Component for sharing, Social Component for challenge responses, UI Management Component for game overlays

**Technology Stack:** Phaser 3.70+ with TypeScript, Webpack asset optimization, WeChat WebView compatibility layer

## WeChat Integration Component

**Responsibility:** Manages all WeChat SDK interactions including authentication, social sharing, payment processing, and friend list access.

**Key Interfaces:**
- AuthenticationService: WeChat login flow, token management, user profile synchronization
- SharingService: Achievement and score sharing to WeChat Moments with viral content generation
- PaymentService: WeChat Pay integration for virtual currency and premium item purchases
- SocialService: Friend list access, challenge invitations, social data synchronization

**Dependencies:** Authentication Component for session management, Game Engine Component for sharing triggers, Store Component for payment processing

**Technology Stack:** WeChat SDK APIs, JWT token handling, secure data encryption/decryption, error handling with circuit breaker pattern

## Authentication Component

**Responsibility:** User authentication, session management, and security enforcement across frontend and backend systems.

**Key Interfaces:**
- UserAuthService: Login/logout flows, token validation, session persistence
- SecurityMiddleware: JWT verification, rate limiting, request authentication
- UserProfileService: Profile management, preference storage, activity tracking
- SessionManager: Cross-component session state management and security enforcement

**Dependencies:** WeChat Integration Component for authentication source, Database Component for user persistence, API Gateway Component for request validation

**Technology Stack:** JWT tokens, Express middleware, MongoDB user storage, Redis session caching, security best practices

## Social Features Component

**Responsibility:** Friend management, leaderboards, challenges, and social interactions that drive viral distribution and user engagement.

**Key Interfaces:**
- LeaderboardService: Friend ranking, score comparison, period-based leaderboards
- ChallengeManager: Challenge creation, delivery, response tracking, and notifications
- FriendshipService: WeChat friend integration, relationship management, privacy controls
- NotificationService: Real-time social notifications and engagement triggers

**Dependencies:** WeChat Integration Component for friend data, Game Engine Component for challenge responses, Database Component for social data persistence, Real-time Communication Component for live notifications

**Technology Stack:** Redis for leaderboard performance, Socket.io for real-time features, MongoDB for social relationships, efficient caching strategies

## Game Data Management Component

**Responsibility:** Game session tracking, score persistence, achievement system, and gameplay analytics supporting both individual progression and social features.

**Key Interfaces:**
- SessionTracker: Game session recording, score validation, duration tracking
- AchievementEngine: Achievement detection, milestone tracking, sharing trigger management
- ProgressManager: User progression, statistics calculation, personal best management
- AnalyticsCollector: Gameplay metrics collection for post-launch optimization

**Dependencies:** Game Engine Component for session data, Social Features Component for social achievements, Database Component for persistence, Authentication Component for user context

**Technology Stack:** MongoDB for flexible game data storage, Redis for real-time statistics, data validation and integrity checks, performance optimized queries

## Store and Monetization Component

**Responsibility:** Virtual economy management, item catalog, purchase processing, and revenue tracking through WeChat Pay integration.

**Key Interfaces:**
- ItemCatalog: Store inventory, pricing, availability, promotional item management
- PurchaseProcessor: WeChat Pay integration, transaction validation, order fulfillment
- VirtualCurrency: Coin management, earning mechanisms, spending validation
- RevenueAnalytics: Transaction tracking, conversion metrics, monetization optimization

**Dependencies:** WeChat Integration Component for payment processing, Authentication Component for purchase authorization, Database Component for transaction records, User Interface Component for store presentation

**Technology Stack:** WeChat Pay SDK, transaction security, MongoDB for purchase history, Redis for inventory caching, fraud prevention measures

## Daily Rewards Component

**Responsibility:** Daily engagement system with progressive rewards, streak tracking, and retention optimization designed for casual gaming patterns.

**Key Interfaces:**
- RewardCalendar: 7-day reward cycle, streak calculation, reset logic
- ClaimProcessor: Reward validation, delivery, inventory updates
- StreakManager: Consecutive day tracking, bonus multipliers, recovery mechanics
- EngagementAnalytics: Retention metrics, reward effectiveness, optimization insights

**Dependencies:** Authentication Component for user context, Store and Monetization Component for reward delivery, Database Component for streak persistence, UI Management Component for reward presentation

**Technology Stack:** MongoDB for reward history, Redis for streak caching, timezone handling for China market, Beijing time reset logic

## Real-time Communication Component

**Responsibility:** Socket.io based real-time features for social notifications, live leaderboard updates, and immediate social feedback.

**Key Interfaces:**
- SocketManager: Connection management, room organization, event routing
- LiveNotifications: Real-time challenge notifications, achievement celebrations, friend activity
- LiveLeaderboards: Real-time score updates, rank changes, competitive notifications
- EventBroadcaster: Social event distribution, viral moment amplification

**Dependencies:** Social Features Component for social events, Authentication Component for connection authorization, Game Data Management Component for live score updates

**Technology Stack:** Socket.io with Redis adapter, connection scaling, event-driven architecture, WebSocket fallback for WeChat WebView

## UI Management Component

**Responsibility:** Frontend UI state management, component coordination, and user experience orchestration across game and social interfaces.

**Key Interfaces:**
- StateManager: Redux-based UI state, persistent data synchronization
- ComponentRegistry: Reusable UI components, WeChat design system adherence
- NavigationController: Screen transitions, modal management, user flow control
- ResponsiveManager: Screen size adaptation, one-handed play optimization

**Dependencies:** Game Engine Component for gameplay UI, Social Features Component for social interfaces, Store and Monetization Component for purchase flows, All backend components for data display

**Technology Stack:** Redux Toolkit for state management, React-like components, Tailwind CSS for styling, mobile-first responsive design

## API Gateway Component

**Responsibility:** Single entry point for all backend services with routing, authentication, rate limiting, and monitoring for optimal WeChat integration.

**Key Interfaces:**
- RequestRouter: Service routing, load balancing, failover management
- AuthenticationMiddleware: JWT validation, user context injection, security enforcement
- RateLimiter: API rate limiting, abuse prevention, WeChat SDK compliance
- MonitoringCollector: Request metrics, performance tracking, error logging

**Dependencies:** All backend service components for routing, Authentication Component for validation, External monitoring services for metrics

**Technology Stack:** Express.js with middleware architecture, rate limiting algorithms, request/response transformation, comprehensive logging

## Database Component

**Responsibility:** Data persistence layer with MongoDB and Redis providing scalable storage for user data, game sessions, social relationships, and real-time features.

**Key Interfaces:**
- UserRepository: User data CRUD operations, profile management, authentication support
- GameDataRepository: Session storage, achievement persistence, analytics data collection
- SocialRepository: Friendship management, challenge storage, leaderboard data
- CacheManager: Redis-based caching, session storage, leaderboard performance optimization

**Dependencies:** All application components require database services, monitoring services for performance metrics

**Technology Stack:** MongoDB with Mongoose ODM, Redis for caching and real-time data, database indexing for performance, backup and recovery strategies

## Component Diagrams

```mermaid
graph TB
    subgraph "Frontend Components"
        GE[Game Engine Component]
        WI[WeChat Integration Component]
        UI[UI Management Component]
    end
    
    subgraph "Backend Services"
        AG[API Gateway Component]
        AUTH[Authentication Component]
        SOCIAL[Social Features Component]
        GAME[Game Data Management Component]
        STORE[Store & Monetization Component]
        REWARDS[Daily Rewards Component]
        RT[Real-time Communication Component]
    end
    
    subgraph "Data Layer"
        DB[Database Component]
    end
    
    %% Frontend Interactions
    GE --> WI
    GE --> UI
    WI --> UI
    
    %% Frontend to Backend
    GE --> AG
    WI --> AG
    UI --> AG
    UI --> RT
    
    %% Backend Service Interactions
    AG --> AUTH
    AG --> SOCIAL
    AG --> GAME
    AG --> STORE
    AG --> REWARDS
    
    AUTH --> DB
    SOCIAL --> DB
    SOCIAL --> RT
    GAME --> DB
    STORE --> DB
    STORE --> WI
    REWARDS --> DB
    RT --> SOCIAL
    RT --> GAME
    
    %% External Dependencies
    WI -.-> WeChat[WeChat SDK]
    STORE -.-> WeChat
```

# External APIs

The WeChat Doodle Jump clone requires integration with several external services to support WeChat ecosystem functionality, payment processing, monitoring, and infrastructure services. All integrations are designed with proper error handling, rate limiting, and fallback mechanisms.

## WeChat MiniProgram API

- **Purpose:** Core platform integration for authentication, social features, and payment processing
- **Documentation:** https://developers.weixin.qq.com/miniprogram/dev/api/
- **Base URL(s):** https://api.weixin.qq.com/
- **Authentication:** App ID and App Secret for server-side calls, user authorization for client-side
- **Rate Limits:** 100,000 calls/day for basic APIs, 10,000/day for advanced features

**Key Endpoints Used:**
- `POST /sns/jscode2session` - Exchange login code for session key and OpenID
- `GET /cgi-bin/token` - Get access token for server-side API calls
- `POST /wxa/msg_sec_check` - Content security check for user-generated content
- `POST /wxa/img_sec_check` - Image content security check

**Integration Notes:** Requires ICP license for production deployment in China. Rate limiting must be implemented to avoid API quota exhaustion. Session keys expire and require refresh handling.

## WeChat Pay API

- **Purpose:** Payment processing for virtual currency and premium item purchases
- **Documentation:** https://pay.weixin.qq.com/wiki/doc/apiv3/index.shtml
- **Base URL(s):** https://api.mch.weixin.qq.com/
- **Authentication:** Merchant ID, API key, and certificate-based authentication
- **Rate Limits:** 600 calls/minute for payment creation, 6000/minute for query operations

**Key Endpoints Used:**
- `POST /v3/pay/transactions/jsapi` - Create JSAPI payment order
- `GET /v3/pay/transactions/id/{transaction_id}` - Query payment status
- `POST /v3/pay/transactions/out-trade-no/{out_trade_no}/close` - Close unpaid order
- `POST /v3/certificates` - Download platform certificates for signature verification

**Integration Notes:** Requires merchant account registration and compliance verification. All requests must be digitally signed. Webhook notifications for payment status updates must be properly validated.

## Alibaba Cloud Monitor API

- **Purpose:** Application performance monitoring, alerting, and metrics collection
- **Documentation:** https://www.alibabacloud.com/help/en/cms/
- **Base URL(s):** https://cms.{region}.aliyuncs.com/
- **Authentication:** AccessKey ID and AccessKey Secret with signature-based authentication
- **Rate Limits:** 500 API calls/minute for metric reporting, 100/minute for alarm management

**Key Endpoints Used:**
- `POST /` - PutCustomMetric for custom application metrics
- `POST /` - PutMetricData for batch metric reporting
- `POST /` - CreateAlarm for automated alerting setup
- `GET /` - DescribeMetricList for historical metrics retrieval

**Integration Notes:** Metrics should be batched to optimize API usage. Alarm thresholds must be configured for key performance indicators. Integration with log service for comprehensive observability.

## Alibaba Cloud Log Service API

- **Purpose:** Centralized logging, log analysis, and audit trail management
- **Documentation:** https://www.alibabacloud.com/help/en/sls/
- **Base URL(s):** https://{project}.{region}.log.aliyuncs.com/
- **Authentication:** AccessKey-based with request signing and optional RAM roles
- **Rate Limits:** 5,000 writes/minute per logstore, 10,000 reads/minute

**Key Endpoints Used:**
- `POST /logstores/{logstore}/shards/lb` - Write log entries in batch
- `GET /logstores/{logstore}/shards` - List available shards for parallel writing
- `GET /logstores/{logstore}?type=histogram` - Query log statistics and analysis
- `POST /logstores/{logstore}/index` - Configure search and analysis indexes

**Integration Notes:** Use structured JSON logging for optimal search performance. Implement log level filtering to manage storage costs. Configure retention policies based on compliance requirements.

## Alibaba Cloud Object Storage Service (OSS) API

- **Purpose:** Game asset storage, user-generated content, and CDN-backed file delivery
- **Documentation:** https://www.alibabacloud.com/help/en/oss/
- **Base URL(s):** https://{bucket}.{region}.aliyuncs.com/
- **Authentication:** AccessKey-based authentication with optional STS temporary credentials
- **Rate Limits:** 2,000 PUT/DELETE requests per second, 30,000 GET requests per second

**Key Endpoints Used:**
- `PUT /{object}` - Upload game assets, achievement images, user avatars
- `GET /{object}` - Retrieve assets with CDN acceleration
- `POST /?delete` - Batch delete for cleanup operations
- `GET /?list-type=2` - List objects for asset management

**Integration Notes:** Configure CDN for global asset delivery. Implement proper access controls for user-generated content. Use lifecycle policies for cost optimization of temporary files.

## WeChat Content Security API

- **Purpose:** Content moderation for user-generated content, chat messages, and shared content
- **Documentation:** https://developers.weixin.qq.com/miniprogram/dev/api-backend/open-api/sec-check/
- **Base URL(s):** https://api.weixin.qq.com/
- **Authentication:** Access token from WeChat API
- **Rate Limits:** 1,000 calls/day for text check, 100 calls/minute for image check

**Key Endpoints Used:**
- `POST /wxa/msg_sec_check` - Text content security verification
- `POST /wxa/img_sec_check` - Image content security verification
- `POST /wxa/media_check_async` - Advanced media content checking

**Integration Notes:** Required for China compliance. Implement async processing for media checks. Cache results to avoid repeated API calls for identical content. Have fallback moderation for API failures.

## MongoDB Atlas API (Optional - if using cloud MongoDB)

- **Purpose:** Database management, monitoring, and automated operations
- **Documentation:** https://docs.atlas.mongodb.com/api/
- **Base URL(s):** https://cloud.mongodb.com/api/atlas/v1.0/
- **Authentication:** API key pairs with digest authentication
- **Rate Limits:** 100 requests/minute for management operations

**Key Endpoints Used:**
- `GET /groups/{groupId}/clusters/{clusterName}` - Monitor cluster status
- `POST /groups/{groupId}/clusters/{clusterName}/backup/snapshots` - Create backups
- `GET /groups/{groupId}/processes` - Monitor database performance metrics

**Integration Notes:** Used for production database management. Implement backup automation and monitoring integration. Consider using for database scaling operations.

## Redis Cloud API (Optional - if using cloud Redis)

- **Purpose:** Cache management, performance monitoring, and scaling operations
- **Documentation:** Varies by provider (Redis Labs, Alibaba Cloud ApsaraDB)
- **Base URL(s):** Provider-specific endpoints
- **Authentication:** API keys or cloud provider authentication
- **Rate Limits:** Provider-specific limits

**Key Endpoints Used:**
- Provider-specific cache management and monitoring endpoints
- Performance metrics and alerting configuration
- Scaling and configuration management

**Integration Notes:** Primarily for production cache management and monitoring. Local Redis sufficient for development and MVP deployment.

# Core Workflows

These sequence diagrams illustrate the critical user journeys and system interactions for the WeChat Doodle Jump clone, showing how components work together to deliver the social-first gaming experience.

## User Authentication and First-Time Login Workflow

```mermaid
sequenceDiagram
    participant U as User
    participant WMP as WeChat MiniProgram
    participant WI as WeChat Integration
    participant API as API Gateway
    participant AUTH as Auth Service
    participant DB as Database
    participant WA as WeChat API

    U->>WMP: Launch game from WeChat
    WMP->>WI: Initialize WeChat SDK
    WI->>WA: Request authorization
    WA-->>WI: Return authorization code
    WI->>API: POST /auth/wechat {code, encryptedData}
    API->>AUTH: Validate WeChat credentials
    AUTH->>WA: Exchange code for session
    WA-->>AUTH: Return openid, session_key
    
    alt New User
        AUTH->>WA: Decrypt user profile data
        AUTH->>DB: Create new user record
        DB-->>AUTH: User created successfully
        AUTH->>DB: Initialize default achievements
        AUTH->>API: Return JWT + user profile
    else Existing User
        AUTH->>DB: Update last active timestamp
        AUTH->>API: Return JWT + user profile
    end
    
    API-->>WI: Authentication successful
    WI->>WI: Store JWT token locally
    WI-->>U: Display main game menu
    
    Note over U,WA: User is now authenticated and ready to play
```

## Complete Game Session with Social Features Workflow

```mermaid
sequenceDiagram
    participant U as User
    participant GE as Game Engine
    participant API as API Gateway
    participant GAME as Game Service
    participant SOCIAL as Social Service
    participant RT as Real-time Comm
    participant WI as WeChat Integration
    participant DB as Database
    participant REDIS as Redis Cache

    U->>GE: Start new game
    GE->>GE: Initialize game state
    GE->>GE: Begin gameplay loop
    
    loop During Gameplay
        U->>GE: Player input (jump, move)
        GE->>GE: Update physics, score
        GE->>GE: Check achievements
    end
    
    GE->>GE: Game over detected
    GE->>API: POST /games/sessions {score, duration, platforms}
    API->>GAME: Process game session
    
    par Achievement Processing
        GAME->>GAME: Check for new achievements
        alt New Achievement Unlocked
            GAME->>DB: Save achievement record
            GAME->>RT: Broadcast achievement event
            RT-->>GE: Real-time achievement notification
            GE-->>U: Display achievement popup
        end
    and Leaderboard Update
        GAME->>REDIS: Update user high score
        GAME->>SOCIAL: Update friend leaderboards
        SOCIAL->>REDIS: Refresh leaderboard cache
        alt New Personal Best
            SOCIAL->>RT: Broadcast leaderboard update
            RT-->>Friends: Notify friends of new high score
        end
    and Social Sharing Trigger
        alt Score worthy of sharing
            GE-->>U: Offer to share achievement
            U->>WI: Accept sharing prompt
            WI->>API: POST /achievements/{id}/share
            API->>WI: Return share data with viral content
            WI->>WeChat: Share to WeChat Moments
        end
    end
    
    API-->>GE: Session recorded with achievements
    GE-->>U: Display game over screen with stats
    
    Note over U,REDIS: Game session complete with social amplification
```

## Friend Challenge Creation and Response Workflow

```mermaid
sequenceDiagram
    participant Challenger as Challenger
    participant Target as Target Friend
    participant API as API Gateway
    participant SOCIAL as Social Service
    participant RT as Real-time Comm
    participant WI as WeChat Integration
    participant GE as Game Engine
    participant DB as Database

    Challenger->>API: POST /social/challenges {targetId, score, message}
    API->>SOCIAL: Create challenge record
    SOCIAL->>DB: Store challenge data
    SOCIAL->>RT: Broadcast challenge event
    
    par Challenge Notification
        RT-->>Target: Real-time challenge notification
        SOCIAL->>WI: Prepare WeChat notification
        WI->>WeChat: Send friend challenge message
    end
    
    Target->>API: GET /social/challenges (view received challenges)
    API->>SOCIAL: Retrieve challenge details
    SOCIAL-->>Target: Display challenge information
    
    Target->>GE: Accept challenge and start game
    GE->>GE: Display challenge target score
    GE->>GE: Execute gameplay with challenge context
    
    alt Challenge Completed Successfully
        Target->>API: POST /games/sessions {challengeId, score}
        API->>SOCIAL: Process challenge response
        SOCIAL->>DB: Update challenge status to 'completed'
        SOCIAL->>RT: Broadcast challenge completion
        
        par Success Notifications
            RT-->>Challenger: Challenge completed notification
            RT-->>Target: Success celebration
            SOCIAL->>WI: Prepare success sharing content
            WI->>WeChat: Share challenge victory
        end
    else Challenge Failed
        Target->>API: POST /games/sessions {challengeId, score}
        API->>SOCIAL: Process challenge response
        SOCIAL->>DB: Update challenge status to 'failed'
        SOCIAL->>RT: Broadcast challenge attempt
        RT-->>Challenger: Challenge attempt notification
    end
    
    Note over Challenger,DB: Challenge cycle complete with viral sharing
```

## WeChat Pay Purchase Workflow

```mermaid
sequenceDiagram
    participant U as User
    participant UI as UI Component
    participant API as API Gateway
    participant STORE as Store Service
    participant WI as WeChat Integration
    participant WP as WeChat Pay
    participant DB as Database

    U->>UI: Browse store items
    UI->>API: GET /store/items
    API->>STORE: Retrieve available items
    STORE-->>UI: Display item catalog
    
    U->>UI: Select item for purchase
    UI->>API: POST /store/purchase {itemId}
    API->>STORE: Initiate purchase process
    
    STORE->>STORE: Validate item and user
    STORE->>DB: Create pending purchase record
    STORE->>WI: Request WeChat Pay order
    WI->>WP: Create JSAPI payment order
    WP-->>WI: Return payment parameters
    WI-->>STORE: Payment order created
    STORE-->>API: Return payment data
    API-->>UI: Payment ready for user
    
    UI->>WI: Trigger WeChat Pay flow
    WI->>WP: Execute payment with user
    U->>WP: Complete payment in WeChat
    
    alt Payment Successful
        WP->>STORE: Webhook notification (payment success)
        STORE->>DB: Update purchase status to 'completed'
        STORE->>STORE: Add virtual currency/items to user account
        STORE->>UI: Payment success notification
        UI-->>U: Display purchase confirmation
    else Payment Failed
        WP->>STORE: Webhook notification (payment failed)
        STORE->>DB: Update purchase status to 'failed'
        STORE-->>UI: Payment failure notification
        UI-->>U: Display error message
    end
    
    Note over U,DB: Purchase workflow with proper transaction handling
```

## Daily Rewards Claim Workflow

```mermaid
sequenceDiagram
    participant U as User
    participant UI as UI Component
    participant API as API Gateway
    participant REWARDS as Rewards Service
    participant STORE as Store Service
    participant DB as Database
    participant REDIS as Redis Cache

    U->>UI: Open daily rewards screen
    UI->>API: GET /rewards/daily
    API->>REWARDS: Check reward status
    
    par Status Check
        REWARDS->>DB: Get user streak data
        REWARDS->>REDIS: Check last claim timestamp
    end
    
    REWARDS->>REWARDS: Calculate current streak and availability
    REWARDS-->>UI: Return reward status
    UI-->>U: Display reward calendar with claim status
    
    alt Reward Available
        U->>UI: Claim daily reward
        UI->>API: POST /rewards/daily
        API->>REWARDS: Process reward claim
        
        REWARDS->>REWARDS: Validate claim eligibility
        REWARDS->>DB: Update streak counter
        REWARDS->>REDIS: Update last claim timestamp
        
        par Reward Delivery
            REWARDS->>STORE: Add virtual currency to account
            REWARDS->>DB: Record reward transaction
        and Achievement Check
            REWARDS->>REWARDS: Check streak achievements
            alt Streak Milestone Reached
                REWARDS->>DB: Grant streak achievement
                REWARDS-->>UI: Bonus achievement notification
            end
        end
        
        REWARDS-->>API: Reward claimed successfully
        API-->>UI: Return reward details
        UI-->>U: Display reward claim animation
        
    else Reward Already Claimed
        UI-->>U: Show "already claimed" message
        
    else Reward Not Yet Available
        UI-->>U: Show countdown to next reward
    end
    
    Note over U,REDIS: Daily engagement cycle complete
```

## Real-time Leaderboard Update Workflow

```mermaid
sequenceDiagram
    participant U1 as User 1
    participant U2 as Friend
    participant U3 as Friend
    participant GE as Game Engine
    participant API as API Gateway
    participant SOCIAL as Social Service
    participant RT as Real-time Comm
    participant REDIS as Redis Cache

    U1->>GE: Complete game with high score
    GE->>API: POST /games/sessions {score}
    API->>SOCIAL: Process new score
    
    SOCIAL->>REDIS: Check current leaderboard position
    REDIS-->>SOCIAL: Return current rankings
    
    alt New High Score for User
        SOCIAL->>REDIS: Update user's high score
        SOCIAL->>REDIS: Recalculate friend leaderboard
        SOCIAL->>RT: Broadcast leaderboard update event
        
        par Real-time Notifications
            RT-->>U2: Friend achieved new high score
            RT-->>U3: Leaderboard position changed
            RT-->>U1: New personal best confirmation
        end
        
        par UI Updates
            RT->>GE: Update leaderboard display for all users
            GE-->>U1: Show new rank celebration
            GE-->>U2: Update friend leaderboard UI
            GE-->>U3: Update friend leaderboard UI
        end
    else Score doesn't change rankings
        SOCIAL->>REDIS: Update user's recent score only
        RT-->>U1: Game recorded notification
    end
    
    Note over U1,REDIS: Live competitive experience maintained
```

# Database Schema

This section transforms the conceptual data models into concrete MongoDB schemas with appropriate indexes, constraints, and performance optimizations for the WeChat Doodle Jump clone.

## MongoDB Collections Schema

### Users Collection

```javascript
// Collection: users
{
  _id: ObjectId("..."),                    // Primary key
  wechatId: "wx_unique_identifier",        // WeChat OpenID (unique)
  nickname: "张三",                        // WeChat display name
  avatar: "https://wx.avatar.url/...",     // WeChat avatar URL
  totalScore: 125430,                      // Lifetime cumulative score
  highScore: 8950,                         // Personal best score
  gamesPlayed: 247,                        // Total games counter
  coins: 350,                              // Virtual currency balance
  createdAt: ISODate("2024-12-05T10:00:00Z"),
  lastActiveAt: ISODate("2024-12-05T15:30:00Z"),
  dailyStreak: 5,                          // Consecutive daily login streak
  lastStreakDate: ISODate("2024-12-05T00:00:00Z"), // Last reward claim date (Beijing timezone)
  
  // Metadata
  version: 1,                              // Schema version for migrations
  metadata: {
    platform: "wechat_miniprogram",
    region: "cn",
    language: "zh-CN"
  }
}

// Indexes for Users Collection
db.users.createIndex({ "wechatId": 1 }, { unique: true })           // Unique login lookup
db.users.createIndex({ "highScore": -1 })                          // Leaderboard queries
db.users.createIndex({ "lastActiveAt": -1 })                       // User retention analytics
db.users.createIndex({ "dailyStreak": -1, "lastStreakDate": -1 })  // Daily rewards optimization
db.users.createIndex({ "createdAt": 1 })                           // User growth analytics
```

### GameSessions Collection

```javascript
// Collection: game_sessions
{
  _id: ObjectId("..."),                    // Primary key
  userId: ObjectId("..."),                 // Reference to users collection
  score: 8950,                             // Final score achieved
  duration: 180,                           // Game duration in seconds
  platformsJumped: 127,                    // Platforms reached counter
  powerupsUsed: ["spring", "jetpack"],     // Array of powerup types used
  createdAt: ISODate("2024-12-05T15:30:00Z"),
  shared: false,                           // Whether score was shared to WeChat
  challengeId: ObjectId("..."),            // Reference to challenges (optional)
  
  // Gameplay analytics
  analytics: {
    peakHeight: 1500,                      // Highest point reached
    fallDistance: 800,                     // Distance fallen on game over
    averageJumpHeight: 95,                 // Average jump distance
    powerupEffectiveness: {
      "spring": 0.85,                      // Powerup success rate
      "jetpack": 0.92
    }
  },
  
  // Performance tracking
  performance: {
    avgFps: 58.5,                          // Average frame rate
    loadTime: 2.1,                         // Game load time in seconds
    crashOccurred: false                   // Whether game crashed during session
  }
}

// Indexes for GameSessions Collection
db.game_sessions.createIndex({ "userId": 1, "createdAt": -1 })      // User game history
db.game_sessions.createIndex({ "score": -1, "createdAt": -1 })      // High score queries
db.game_sessions.createIndex({ "challengeId": 1 })                  // Challenge responses
db.game_sessions.createIndex({ "createdAt": -1 })                   // Recent games analytics
db.game_sessions.createIndex({ 
  "userId": 1, 
  "score": -1, 
  "createdAt": -1 
}, { background: true })                                             // Compound user performance index
```

### Achievements Collection

```javascript
// Collection: achievements
{
  _id: ObjectId("..."),                    // Primary key
  userId: ObjectId("..."),                 // Reference to users collection
  type: "score",                           // Achievement category
  achievementId: "first_thousand",         // Achievement template identifier
  name: "破千达人",                         // Display name (Chinese)
  description: "首次得分超过1000分",        // Achievement description
  iconUrl: "https://cdn.game.com/icons/first_thousand.png",
  earnedAt: ISODate("2024-12-05T15:30:00Z"),
  shared: false,                           // Whether shared to WeChat
  rarity: "common",                        // Rarity tier
  
  // Achievement context
  context: {
    triggerScore: 1150,                    // Score that triggered achievement
    sessionId: ObjectId("..."),            // Game session reference
    milestone: 1000,                       // Achievement threshold
    isFirstTime: true                      // First time achieving this milestone
  },
  
  // Localization support
  i18n: {
    "zh-CN": {
      name: "破千达人",
      description: "首次得分超过1000分"
    },
    "en": {
      name: "First Thousand",
      description: "Reach 1000 points for the first time"
    }
  }
}

// Indexes for Achievements Collection
db.achievements.createIndex({ "userId": 1, "earnedAt": -1 })        // User achievements timeline
db.achievements.createIndex({ "type": 1, "rarity": 1 })             // Achievement browsing
db.achievements.createIndex({ "achievementId": 1, "userId": 1 }, { unique: true }) // Prevent duplicates
db.achievements.createIndex({ "earnedAt": -1 })                     // Recent achievements feed
```

### Friendships Collection

```javascript
// Collection: friendships
{
  _id: ObjectId("..."),                    // Primary key
  userId: ObjectId("..."),                 // First user reference
  friendId: ObjectId("..."),               // Second user reference (WeChat friend)
  status: "active",                        // Relationship status
  createdAt: ISODate("2024-12-05T10:00:00Z"),
  lastInteractionAt: ISODate("2024-12-05T15:30:00Z"),
  
  // Friendship context
  context: {
    source: "wechat_friend_list",          // How friendship was established
    mutualFriends: 5,                      // Count of mutual friends
    gameInviter: ObjectId("..."),          // Who invited whom to play
    interactions: {
      challengesSent: 3,                   // Challenges sent between friends
      challengesReceived: 2,               // Challenges received
      achievementsShared: 7                // Achievements shared
    }
  },
  
  // Privacy and preferences
  preferences: {
    allowChallenges: true,                 // Accept challenges from this friend
    shareAchievements: true,               // Share achievements with this friend
    leaderboardVisible: true               // Show in friend leaderboards
  }
}

// Indexes for Friendships Collection
db.friendships.createIndex({ "userId": 1, "status": 1 })           // User's active friends
db.friendships.createIndex({ "friendId": 1, "status": 1 })         // Reverse friend lookup
db.friendships.createIndex({ 
  "userId": 1, 
  "friendId": 1 
}, { unique: true })                                                // Prevent duplicate friendships
db.friendships.createIndex({ "lastInteractionAt": -1 })            // Recent interactions
```

### Challenges Collection

```javascript
// Collection: challenges
{
  _id: ObjectId("..."),                    // Primary key
  challengerId: ObjectId("..."),           // User who created challenge
  targetId: ObjectId("..."),               // Friend being challenged
  targetScore: 5000,                       // Score to beat
  message: "能超过我这个分数吗？",           // Personal challenge message
  status: "pending",                       // Challenge lifecycle state
  createdAt: ISODate("2024-12-05T15:00:00Z"),
  expiresAt: ISODate("2024-12-12T15:00:00Z"), // Challenge expiration (7 days)
  completedAt: null,                       // Completion timestamp
  responseScore: null,                     // Target user's response score
  
  // Challenge context
  context: {
    challengeType: "beat_score",           // Type of challenge
    originalSessionId: ObjectId("..."),    // Session that triggered challenge
    attempts: 0,                           // Number of attempts by target
    maxAttempts: 3,                        // Maximum allowed attempts
    difficulty: "medium",                  // Calculated difficulty level
    rewards: {
      success: { coins: 50 },              // Reward for completion
      participation: { coins: 10 }         // Reward for attempting
    }
  },
  
  // Response tracking
  responses: [{
    sessionId: ObjectId("..."),            // Game session reference
    score: 4850,                           // Score achieved
    attemptedAt: ISODate("2024-12-05T16:00:00Z"),
    succeeded: false                       // Whether challenge was met
  }]
}

// Indexes for Challenges Collection
db.challenges.createIndex({ "targetId": 1, "status": 1, "createdAt": -1 })  // User's received challenges
db.challenges.createIndex({ "challengerId": 1, "createdAt": -1 })           // User's sent challenges
db.challenges.createIndex({ "status": 1, "expiresAt": 1 })                 // Cleanup expired challenges
db.challenges.createIndex({ "createdAt": -1 })                             // Recent challenges feed
```

### Purchases Collection

```javascript
// Collection: purchases
{
  _id: ObjectId("..."),                    // Primary key
  userId: ObjectId("..."),                 // Purchasing user reference
  itemType: "coins",                       // Purchase category
  itemId: "coin_pack_100",                 // Specific item identifier
  amount: 6.00,                            // Price in Chinese Yuan
  coins: 100,                              // Virtual currency amount (if applicable)
  wechatTransactionId: "wx_trans_123456",  // WeChat Pay transaction ID
  wechatOrderId: "order_789012",           // Internal order reference
  status: "completed",                     // Payment status
  createdAt: ISODate("2024-12-05T15:00:00Z"),
  completedAt: ISODate("2024-12-05T15:01:00Z"),
  
  // Payment details
  payment: {
    method: "wechat_pay",                  // Payment method
    currency: "CNY",                       // Payment currency
    exchangeRate: 1.0,                     // Currency exchange rate
    paymentChannel: "jsapi",               // WeChat Pay channel type
    merchantId: "mch_123456789",           // WeChat merchant ID
    
    // Transaction verification
    signature: "wx_signature_hash",        // WeChat Pay signature
    certificateSerial: "cert_serial_123",  // Certificate serial number
    verifiedAt: ISODate("2024-12-05T15:01:00Z")
  },
  
  // Item delivery
  delivery: {
    delivered: true,                       // Whether item was delivered
    deliveredAt: ISODate("2024-12-05T15:01:05Z"),
    deliveryMethod: "automatic",           // Delivery mechanism
    failureReason: null                    // Reason if delivery failed
  },
  
  // Audit trail
  audit: {
    ipAddress: "*************",           // User IP for fraud detection
    userAgent: "WeChat/...",               // Client information
    fraudScore: 0.1,                       // Fraud detection score (0-1)
    riskLevel: "low"                       // Risk assessment
  }
}

// Indexes for Purchases Collection
db.purchases.createIndex({ "userId": 1, "createdAt": -1 })         // User purchase history
db.purchases.createIndex({ "wechatTransactionId": 1 }, { unique: true }) // Prevent duplicate transactions
db.purchases.createIndex({ "status": 1, "createdAt": -1 })         // Payment processing queries
db.purchases.createIndex({ "createdAt": -1 })                      // Revenue analytics
db.purchases.createIndex({ "status": 1, "completedAt": 1 })        // Completion tracking
```

## Redis Cache Schema

### Leaderboard Cache Structure

```javascript
// Redis Sorted Sets for Leaderboards
// Key pattern: leaderboard:{type}:{period}:{scope}

// Friend leaderboards (most critical for performance)
"leaderboard:friends:weekly:user_12345" => ZSET {
  "user_12345": 8950,    // User's own score
  "user_67890": 7650,    // Friend scores
  "user_54321": 6890
}

// Global leaderboards (for broader competition)  
"leaderboard:global:monthly" => ZSET {
  "user_12345": 8950,
  "user_98765": 12450,
  "user_11111": 9870
}

// Leaderboard metadata
"leaderboard:meta:friends:weekly:user_12345" => HASH {
  "last_updated": "1701781800",
  "total_friends": "15", 
  "user_rank": "3",
  "expires_at": "1701868200"
}
```

### Session and User Cache

```javascript
// User session data (JWT complement)
"session:user_12345" => HASH {
  "user_id": "12345",
  "nickname": "张三",
  "avatar": "https://wx.avatar.url/...",
  "high_score": "8950",
  "coins": "350",
  "daily_streak": "5",
  "last_active": "1701781800",
  "expires_at": "1701868200"
}

// Daily rewards tracking
"daily_rewards:user_12345" => HASH {
  "current_streak": "5",
  "last_claim_date": "2024-12-05",
  "last_claim_timestamp": "1701781800",
  "next_reward_day": "6",
  "can_claim": "false"
}

// Real-time game statistics
"game_stats:user_12345" => HASH {
  "games_today": "5",
  "best_score_today": "8950", 
  "total_playtime_today": "1800",
  "achievements_today": "2"
}
```

### Social Features Cache

```javascript
// Active challenges cache
"challenges:user_12345:received" => LIST [
  "challenge_id_1",
  "challenge_id_2", 
  "challenge_id_3"
]

"challenges:user_12345:sent" => LIST [
  "challenge_id_4",
  "challenge_id_5"
]

// Challenge details cache
"challenge:challenge_id_1" => HASH {
  "challenger_id": "67890",
  "target_score": "5000",
  "message": "能超过我这个分数吗？",
  "status": "pending",
  "expires_at": "1702386600"
}

// Friend activity feed
"activity:user_12345" => LIST [
  "{\"type\":\"high_score\",\"friend_id\":\"67890\",\"score\":7650,\"timestamp\":1701781800}",
  "{\"type\":\"achievement\",\"friend_id\":\"54321\",\"achievement\":\"first_thousand\",\"timestamp\":1701781700}"
]
```

## Database Performance Optimization

### Sharding Strategy (Future Scaling)

```javascript
// Shard key selection for horizontal scaling
// Primary shard key: userId (ensures user data co-location)

// Users collection: Shard by _id (ObjectId contains timestamp)
sh.shardCollection("gamedb.users", { "_id": 1 })

// Game sessions: Shard by userId for query performance
sh.shardCollection("gamedb.game_sessions", { "userId": 1, "createdAt": -1 })

// Friendships: Shard by userId (queries always include userId)
sh.shardCollection("gamedb.friendships", { "userId": 1 })

// Challenges: Shard by targetId (most queries are for received challenges)
sh.shardCollection("gamedb.challenges", { "targetId": 1 })
```

### Index Optimization Strategy

```javascript
// Compound indexes for common query patterns
db.game_sessions.createIndex({
  "userId": 1,
  "createdAt": -1,
  "score": -1
}, {
  name: "user_sessions_performance",
  background: true
})

// Partial indexes for active data only
db.challenges.createIndex(
  { "targetId": 1, "status": 1, "createdAt": -1 },
  { 
    partialFilterExpression: { 
      "status": { $in: ["pending", "in_progress"] } 
    },
    name: "active_challenges_only"
  }
)

// TTL indexes for automatic cleanup
db.game_sessions.createIndex(
  { "createdAt": 1 },
  { 
    expireAfterSeconds: 7776000,  // 90 days retention
    name: "session_cleanup"
  }
)
```

### Query Optimization Guidelines

```javascript
// Efficient friend leaderboard query
db.users.aggregate([
  // Stage 1: Find user's friends
  { $match: { "_id": ObjectId("user_id") } },
  { $lookup: {
    from: "friendships",
    localField: "_id", 
    foreignField: "userId",
    as: "friendships"
  }},
  
  // Stage 2: Get friend user data with scores
  { $lookup: {
    from: "users",
    localField: "friendships.friendId",
    foreignField: "_id", 
    as: "friends"
  }},
  
  // Stage 3: Sort by high score and limit
  { $unwind: "$friends" },
  { $replaceRoot: { newRoot: "$friends" } },
  { $sort: { "highScore": -1 } },
  { $limit: 20 }
])
```

# Frontend Architecture

The frontend architecture for the WeChat Doodle Jump clone is designed specifically for WeChat WebView environment with Phaser 3 game engine, optimized for mobile performance, social features integration, and the 20MB size constraint.

## Component Architecture

### Component Organization

```
client/
├── src/
│   ├── components/           # Reusable UI components
│   │   ├── common/          # Generic UI components
│   │   │   ├── Button/
│   │   │   │   ├── Button.ts
│   │   │   │   ├── Button.scss
│   │   │   │   └── index.ts
│   │   │   ├── Modal/
│   │   │   ├── Loading/
│   │   │   └── Toast/
│   │   ├── game/            # Game-specific UI components
│   │   │   ├── ScoreDisplay/
│   │   │   ├── GameMenu/
│   │   │   ├── PauseScreen/
│   │   │   └── GameOverScreen/
│   │   └── social/          # Social feature components
│   │       ├── Leaderboard/
│   │       ├── ChallengeCard/
│   │       ├── AchievementBadge/
│   │       └── FriendsList/
│   ├── scenes/              # Phaser 3 game scenes
│   │   ├── BootScene.ts     # Asset loading and initialization
│   │   ├── MenuScene.ts     # Main menu with social features
│   │   ├── GameScene.ts     # Core gameplay scene
│   │   ├── GameOverScene.ts # Game over with sharing options
│   │   └── ShopScene.ts     # Store and purchases
│   ├── game-objects/        # Phaser game entities
│   │   ├── Player.ts        # Player character with physics
│   │   ├── Platform.ts      # Jump platforms with varieties
│   │   ├── Powerup.ts       # Power-up items
│   │   └── Background.ts    # Scrolling background elements
│   ├── managers/            # Game system managers
│   │   ├── GameStateManager.ts
│   │   ├── ScoreManager.ts
│   │   ├── InputManager.ts
│   │   └── AudioManager.ts
│   └── services/            # External service integrations
│       ├── api/             # API client services
│       ├── wechat/          # WeChat SDK integration
│       └── storage/         # Local storage management
```

### Component Template

```typescript
// Example: Button Component Template
import { EventEmitter } from 'events';

export interface ButtonConfig {
  text: string;
  x: number;
  y: number;
  width?: number;
  height?: number;
  style?: ButtonStyle;
  onClick?: () => void;
  disabled?: boolean;
}

export interface ButtonStyle {
  backgroundColor: string;
  textColor: string;
  fontSize: number;
  borderRadius: number;
  padding: number;
}

export class Button extends Phaser.GameObjects.Container {
  private background: Phaser.GameObjects.Rectangle;
  private text: Phaser.GameObjects.Text;
  private config: ButtonConfig;
  private events: EventEmitter;

  constructor(scene: Phaser.Scene, config: ButtonConfig) {
    super(scene, config.x, config.y);
    
    this.config = config;
    this.events = new EventEmitter();
    
    this.createBackground();
    this.createText();
    this.setupInteractivity();
    
    scene.add.existing(this);
  }

  private createBackground(): void {
    const style = this.config.style || this.getDefaultStyle();
    this.background = this.scene.add.rectangle(
      0, 0,
      this.config.width || 200,
      this.config.height || 60,
      Phaser.Display.Color.HexStringToColor(style.backgroundColor).color
    );
    this.add(this.background);
  }

  private createText(): void {
    const style = this.config.style || this.getDefaultStyle();
    this.text = this.scene.add.text(0, 0, this.config.text, {
      fontSize: `${style.fontSize}px`,
      color: style.textColor,
      fontFamily: 'Arial, sans-serif'
    });
    this.text.setOrigin(0.5);
    this.add(this.text);
  }

  private setupInteractivity(): void {
    this.background.setInteractive();
    this.background.on('pointerdown', () => {
      if (!this.config.disabled && this.config.onClick) {
        this.config.onClick();
        this.events.emit('click');
      }
    });

    // Visual feedback for WeChat mobile users
    this.background.on('pointerover', () => this.setAlpha(0.8));
    this.background.on('pointerout', () => this.setAlpha(1.0));
  }

  private getDefaultStyle(): ButtonStyle {
    return {
      backgroundColor: '#4CAF50',
      textColor: '#FFFFFF',
      fontSize: 18,
      borderRadius: 8,
      padding: 12
    };
  }

  public updateText(newText: string): void {
    this.text.setText(newText);
  }

  public setDisabled(disabled: boolean): void {
    this.config.disabled = disabled;
    this.setAlpha(disabled ? 0.5 : 1.0);
  }
}
```

## State Management Architecture

### State Structure

```typescript
// Redux Toolkit State Structure
export interface RootState {
  // User authentication and profile
  auth: {
    isAuthenticated: boolean;
    user: User | null;
    token: string | null;
    loading: boolean;
    error: string | null;
  };

  // Game state and progress
  game: {
    currentScore: number;
    highScore: number;
    gameSession: GameSession | null;
    achievements: Achievement[];
    gameStats: {
      gamesPlayed: number;
      totalScore: number;
      averageScore: number;
      playTime: number;
    };
  };

  // Social features and interactions
  social: {
    friends: User[];
    friendLeaderboard: LeaderboardEntry[];
    challenges: {
      received: Challenge[];
      sent: Challenge[];
    };
    recentActivity: SocialActivity[];
    loading: {
      friends: boolean;
      leaderboard: boolean;
      challenges: boolean;
    };
  };

  // Store and monetization
  store: {
    items: StoreItem[];
    userCoins: number;
    purchases: Purchase[];
    cart: CartItem[];
    loading: boolean;
  };

  // Daily rewards and engagement
  rewards: {
    currentStreak: number;
    canClaim: boolean;
    todaysClaimed: boolean;
    nextReward: RewardInfo;
    rewardHistory: ClaimedReward[];
  };

  // UI state management
  ui: {
    activeScene: string;
    modals: {
      achievement: AchievementModal | null;
      challenge: ChallengeModal | null;
      shop: boolean;
      leaderboard: boolean;
    };
    notifications: Notification[];
    loading: boolean;
    connectionStatus: 'connected' | 'disconnected' | 'reconnecting';
  };

  // WeChat integration state
  wechat: {
    isInitialized: boolean;
    shareData: ShareData | null;
    paymentInProgress: boolean;
    friendListPermission: boolean;
  };
}
```

### State Management Patterns

```typescript
// Redux Toolkit Slices Example - Game Slice
import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { gameApi } from '../services/api/gameApi';

// Async thunk for submitting game session
export const submitGameSession = createAsyncThunk(
  'game/submitSession',
  async (sessionData: GameSessionData, { getState, rejectWithValue }) => {
    try {
      const state = getState() as RootState;
      const response = await gameApi.submitSession({
        ...sessionData,
        userId: state.auth.user?.id
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to submit session');
    }
  }
);

// Game slice definition
const gameSlice = createSlice({
  name: 'game',
  initialState: {
    currentScore: 0,
    highScore: 0,
    gameSession: null,
    achievements: [],
    gameStats: {
      gamesPlayed: 0,
      totalScore: 0,
      averageScore: 0,
      playTime: 0
    },
    loading: false,
    error: null
  },
  reducers: {
    // Synchronous actions for real-time game updates
    updateCurrentScore: (state, action: PayloadAction<number>) => {
      state.currentScore = action.payload;
    },
    
    startNewGame: (state) => {
      state.currentScore = 0;
      state.gameSession = {
        startTime: Date.now(),
        score: 0,
        duration: 0,
        platformsJumped: 0,
        powerupsUsed: []
      };
    },
    
    addAchievement: (state, action: PayloadAction<Achievement>) => {
      state.achievements.push(action.payload);
    },
    
    updateGameStats: (state, action: PayloadAction<Partial<GameStats>>) => {
      state.gameStats = { ...state.gameStats, ...action.payload };
    }
  },
  extraReducers: (builder) => {
    // Handle async thunk states
    builder
      .addCase(submitGameSession.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(submitGameSession.fulfilled, (state, action) => {
        state.loading = false;
        if (action.payload.personalBest) {
          state.highScore = action.payload.session.score;
        }
        if (action.payload.newAchievements) {
          state.achievements.push(...action.payload.newAchievements);
        }
      })
      .addCase(submitGameSession.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  }
});

export const { updateCurrentScore, startNewGame, addAchievement, updateGameStats } = gameSlice.actions;
export default gameSlice.reducer;
```

## Routing Architecture

### Route Organization

```typescript
// Scene-based routing for Phaser game
export enum GameScenes {
  BOOT = 'BootScene',
  MENU = 'MenuScene', 
  GAME = 'GameScene',
  GAME_OVER = 'GameOverScene',
  LEADERBOARD = 'LeaderboardScene',
  SHOP = 'ShopScene',
  ACHIEVEMENTS = 'AchievementsScene',
  SETTINGS = 'SettingsScene'
}

// Scene transition manager
export class SceneManager {
  private game: Phaser.Game;
  private currentScene: string;
  private sceneHistory: string[];

  constructor(game: Phaser.Game) {
    this.game = game;
    this.currentScene = GameScenes.BOOT;
    this.sceneHistory = [];
  }

  public transitionTo(sceneKey: string, data?: any): void {
    // Add current scene to history for back navigation
    if (this.currentScene !== sceneKey) {
      this.sceneHistory.push(this.currentScene);
    }

    // Perform scene transition with data passing
    this.game.scene.transition({
      from: this.currentScene,
      to: sceneKey,
      duration: 300,
      data: data || {}
    });

    this.currentScene = sceneKey;
    
    // Update Redux state for UI synchronization
    store.dispatch(setActiveScene(sceneKey));
  }

  public goBack(): void {
    if (this.sceneHistory.length > 0) {
      const previousScene = this.sceneHistory.pop();
      this.transitionTo(previousScene!);
    }
  }

  public getCurrentScene(): string {
    return this.currentScene;
  }
}
```

### Protected Route Pattern

```typescript
// Authentication guard for sensitive scenes
export class AuthGuard {
  private store: Store<RootState>;

  constructor(store: Store<RootState>) {
    this.store = store;
  }

  public canAccessScene(sceneKey: string): boolean {
    const state = this.store.getState();
    const isAuthenticated = state.auth.isAuthenticated;

    // Scenes that require authentication
    const protectedScenes = [
      GameScenes.GAME,
      GameScenes.LEADERBOARD,
      GameScenes.SHOP,
      GameScenes.ACHIEVEMENTS
    ];

    if (protectedScenes.includes(sceneKey as GameScenes)) {
      return isAuthenticated;
    }

    return true;
  }

  public redirectToLogin(): void {
    // Trigger WeChat authentication flow
    store.dispatch(initiateWeChatAuth());
  }
}

// Usage in scene transitions
export class MenuScene extends Phaser.Scene {
  private authGuard: AuthGuard;
  private sceneManager: SceneManager;

  constructor() {
    super({ key: GameScenes.MENU });
  }

  init() {
    this.authGuard = new AuthGuard(store);
    this.sceneManager = new SceneManager(this.game);
  }

  private onPlayButtonClick(): void {
    if (this.authGuard.canAccessScene(GameScenes.GAME)) {
      this.sceneManager.transitionTo(GameScenes.GAME);
    } else {
      this.authGuard.redirectToLogin();
    }
  }
}
```

## Frontend Services Layer

### API Client Setup

```typescript
// Axios-based API client with WeChat integration
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { store } from '../store';
import { logout, refreshToken } from '../store/slices/authSlice';

class ApiClient {
  private client: AxiosInstance;
  private baseURL: string;

  constructor() {
    this.baseURL = process.env.NODE_ENV === 'production' 
      ? 'https://api.wechat-doodle-jump.com/v1'
      : 'http://localhost:3000/v1';

    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
        'X-Platform': 'wechat-miniprogram'
      }
    });

    this.setupInterceptors();
  }

  private setupInterceptors(): void {
    // Request interceptor for authentication
    this.client.interceptors.request.use(
      (config: AxiosRequestConfig) => {
        const state = store.getState();
        const token = state.auth.token;
        
        if (token) {
          config.headers = {
            ...config.headers,
            Authorization: `Bearer ${token}`
          };
        }

        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response: AxiosResponse) => response,
      async (error) => {
        const originalRequest = error.config;

        // Handle token expiration
        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;
          
          try {
            await store.dispatch(refreshToken());
            const newToken = store.getState().auth.token;
            originalRequest.headers.Authorization = `Bearer ${newToken}`;
            return this.client(originalRequest);
          } catch (refreshError) {
            store.dispatch(logout());
            return Promise.reject(refreshError);
          }
        }

        // Handle network errors for WeChat environment
        if (!error.response) {
          return Promise.reject({
            message: '网络连接失败，请检查网络设置',
            code: 'NETWORK_ERROR'
          });
        }

        return Promise.reject(error);
      }
    );
  }

  // HTTP method wrappers
  public async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.get<T>(url, config);
    return response.data;
  }

  public async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.post<T>(url, data, config);
    return response.data;
  }

  public async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.put<T>(url, data, config);
    return response.data;
  }

  public async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.delete<T>(url, config);
    return response.data;
  }
}

export const apiClient = new ApiClient();
```

### Service Example

```typescript
// Game API service for score submission and achievements
import { apiClient } from './apiClient';
import { GameSession, Achievement, LeaderboardEntry } from '../../types';

export class GameApiService {
  // Submit completed game session
  public async submitSession(sessionData: {
    score: number;
    duration: number;
    platformsJumped: number;
    powerupsUsed: string[];
    challengeId?: string;
  }): Promise<{
    session: GameSession;
    newAchievements: Achievement[];
    personalBest: boolean;
  }> {
    return apiClient.post('/games/sessions', sessionData);
  }

  // Get friend leaderboard with caching
  public async getFriendLeaderboard(period: 'weekly' | 'monthly' | 'alltime' = 'weekly'): Promise<{
    leaderboard: LeaderboardEntry[];
    userRank: number;
  }> {
    return apiClient.get(`/leaderboards/friends?period=${period}`);
  }

  // Get user achievements with pagination
  public async getAchievements(type?: string): Promise<Achievement[]> {
    const url = type ? `/achievements?type=${type}` : '/achievements';
    return apiClient.get(url);
  }

  // Share achievement to WeChat
  public async shareAchievement(achievementId: string): Promise<{
    shareData: {
      title: string;
      imageUrl: string;
      path: string;
    };
  }> {
    return apiClient.post(`/achievements/${achievementId}/share`);
  }

  // Create friend challenge
  public async createChallenge(challengeData: {
    targetId: string;
    targetScore: number;
    message: string;
  }): Promise<Challenge> {
    return apiClient.post('/social/challenges', challengeData);
  }
}

export const gameApi = new GameApiService();
```

# Backend Architecture

The backend architecture for the WeChat Doodle Jump clone implements a microservices-within-monorepo approach using Node.js and Express, optimized for WeChat ecosystem integration, social features, and scalable performance.

## Service Architecture

### Traditional Server Architecture

The backend uses a traditional Express server architecture with modular service organization, chosen for rapid development and operational simplicity while maintaining clear service boundaries.

#### Controller/Route Organization

```
server/
├── src/
│   ├── controllers/         # Request handlers and business logic
│   │   ├── auth/
│   │   │   ├── AuthController.ts
│   │   │   └── WeChat AuthController.ts
│   │   ├── game/
│   │   │   ├── GameController.ts
│   │   │   └── SessionController.ts
│   │   ├── social/
│   │   │   ├── FriendsController.ts
│   │   │   ├── LeaderboardController.ts
│   │   │   └── ChallengeController.ts
│   │   ├── store/
│   │   │   ├── StoreController.ts
│   │   │   └── PurchaseController.ts
│   │   └── rewards/
│   │       └── DailyRewardsController.ts
│   ├── services/            # Business logic layer
│   │   ├── AuthService.ts
│   │   ├── GameService.ts
│   │   ├── SocialService.ts
│   │   ├── PaymentService.ts
│   │   └── RewardsService.ts
│   ├── repositories/        # Data access layer
│   │   ├── UserRepository.ts
│   │   ├── GameSessionRepository.ts
│   │   ├── SocialRepository.ts
│   │   └── PurchaseRepository.ts
│   ├── middleware/          # Express middleware
│   │   ├── auth.ts
│   │   ├── validation.ts
│   │   ├── rateLimit.ts
│   │   └── error.ts
│   ├── routes/              # Route definitions
│   │   ├── auth.ts
│   │   ├── game.ts
│   │   ├── social.ts
│   │   ├── store.ts
│   │   └── rewards.ts
│   └── utils/               # Shared utilities
│       ├── wechatSDK.ts
│       ├── redis.ts
│       ├── mongodb.ts
│       └── logger.ts
```

#### Controller Template

```typescript
// Example: GameController with WeChat integration
import { Request, Response, NextFunction } from 'express';
import { GameService } from '../services/GameService';
import { SocialService } from '../services/SocialService';
import { AuthenticatedRequest } from '../types/auth';
import { ValidationError, BusinessLogicError } from '../utils/errors';
import { logger } from '../utils/logger';

export class GameController {
  private gameService: GameService;
  private socialService: SocialService;

  constructor() {
    this.gameService = new GameService();
    this.socialService = new SocialService();
  }

  /**
   * Submit completed game session with achievement detection
   * POST /api/games/sessions
   */
  public submitSession = async (
    req: AuthenticatedRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const { score, duration, platformsJumped, powerupsUsed, challengeId } = req.body;
      const userId = req.user!.id;

      // Validate session data
      if (score < 0 || duration < 1 || platformsJumped < 0) {
        throw new ValidationError('Invalid game session data');
      }

      // Process game session with business logic
      const result = await this.gameService.processGameSession({
        userId,
        score,
        duration,
        platformsJumped,
        powerupsUsed: powerupsUsed || [],
        challengeId
      });

      // Update social features in parallel
      const socialUpdates = await Promise.allSettled([
        this.socialService.updateLeaderboards(userId, score),
        this.socialService.notifyFriendsOfHighScore(userId, score, result.personalBest),
        challengeId ? this.socialService.processChallengeResponse(challengeId, score) : null
      ]);

      // Log any social update failures
      socialUpdates.forEach((update, index) => {
        if (update.status === 'rejected') {
          logger.warn(`Social update ${index} failed`, { 
            userId, 
            error: update.reason 
          });
        }
      });

      logger.info('Game session submitted successfully', {
        userId,
        score,
        personalBest: result.personalBest,
        newAchievements: result.newAchievements.length
      });

      res.status(201).json({
        session: result.session,
        newAchievements: result.newAchievements,
        personalBest: result.personalBest
      });

    } catch (error) {
      next(error);
    }
  };

  /**
   * Get user's game statistics and progress
   * GET /api/games/stats
   */
  public getGameStats = async (
    req: AuthenticatedRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const userId = req.user!.id;
      const stats = await this.gameService.getUserGameStats(userId);

      res.json(stats);
    } catch (error) {
      next(error);
    }
  };

  /**
   * Get user's recent game sessions
   * GET /api/games/sessions?limit=10&offset=0
   */
  public getRecentSessions = async (
    req: AuthenticatedRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const userId = req.user!.id;
      const limit = Math.min(parseInt(req.query.limit as string) || 10, 50);
      const offset = parseInt(req.query.offset as string) || 0;

      const sessions = await this.gameService.getUserSessions(userId, limit, offset);

      res.json(sessions);
    } catch (error) {
      next(error);
    }
  };
}
```

## Database Architecture

### Schema Design

The database architecture uses MongoDB with optimized schema design for social gaming features and WeChat integration.

```typescript
// MongoDB connection and configuration
import mongoose from 'mongoose';
import { logger } from '../utils/logger';

export class DatabaseService {
  private connectionString: string;
  private options: mongoose.ConnectOptions;

  constructor() {
    this.connectionString = process.env.MONGODB_URI || 'mongodb://localhost:27017/wechat-doodle-jump';
    this.options = {
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
      bufferCommands: false,
      bufferMaxEntries: 0
    };
  }

  public async connect(): Promise<void> {
    try {
      await mongoose.connect(this.connectionString, this.options);
      logger.info('MongoDB connected successfully');
      
      // Setup connection event handlers
      mongoose.connection.on('error', (error) => {
        logger.error('MongoDB connection error', error);
      });

      mongoose.connection.on('disconnected', () => {
        logger.warn('MongoDB disconnected');
      });

      mongoose.connection.on('reconnected', () => {
        logger.info('MongoDB reconnected');
      });

    } catch (error) {
      logger.error('Failed to connect to MongoDB', error);
      throw error;
    }
  }

  public async disconnect(): Promise<void> {
    await mongoose.disconnect();
    logger.info('MongoDB disconnected');
  }
}
```

### Data Access Layer

```typescript
// Repository pattern implementation for User data
import { User, UserDocument } from '../models/User';
import { BaseRepository } from './BaseRepository';

export class UserRepository extends BaseRepository<UserDocument> {
  constructor() {
    super(User);
  }

  /**
   * Find user by WeChat ID with caching
   */
  public async findByWeChatId(wechatId: string): Promise<UserDocument | null> {
    // Check Redis cache first
    const cacheKey = `user:wechat:${wechatId}`;
    const cached = await this.redisClient.get(cacheKey);
    
    if (cached) {
      return JSON.parse(cached);
    }

    // Query database
    const user = await this.model.findOne({ wechatId }).lean();
    
    if (user) {
      // Cache for 1 hour
      await this.redisClient.setex(cacheKey, 3600, JSON.stringify(user));
    }

    return user;
  }

  /**
   * Update user's high score and game statistics
   */
  public async updateGameStats(
    userId: string, 
    updates: {
      highScore?: number;
      totalScore?: number;
      gamesPlayed?: number;
      lastActiveAt?: Date;
    }
  ): Promise<UserDocument> {
    const updateQuery: any = {
      lastActiveAt: new Date()
    };

    // Only update high score if it's actually higher
    if (updates.highScore !== undefined) {
      updateQuery.$max = { highScore: updates.highScore };
    }

    // Increment total score and games played
    if (updates.totalScore !== undefined || updates.gamesPlayed !== undefined) {
      updateQuery.$inc = {};
      if (updates.totalScore) updateQuery.$inc.totalScore = updates.totalScore;
      if (updates.gamesPlayed) updateQuery.$inc.gamesPlayed = updates.gamesPlayed;
    }

    const user = await this.model.findByIdAndUpdate(
      userId,
      updateQuery,
      { new: true, runValidators: true }
    );

    if (!user) {
      throw new Error('User not found');
    }

    // Invalidate cache
    if (user.wechatId) {
      await this.redisClient.del(`user:wechat:${user.wechatId}`);
    }

    return user;
  }

  /**
   * Update daily streak for rewards system
   */
  public async updateDailyStreak(userId: string, streakCount: number): Promise<UserDocument> {
    const user = await this.model.findByIdAndUpdate(
      userId,
      {
        dailyStreak: streakCount,
        lastStreakDate: new Date(),
        lastActiveAt: new Date()
      },
      { new: true, runValidators: true }
    );

    if (!user) {
      throw new Error('User not found');
    }

    return user;
  }

  /**
   * Get top users for global leaderboard
   */
  public async getTopUsers(limit: number = 50): Promise<UserDocument[]> {
    return this.model
      .find({ highScore: { $gt: 0 } })
      .sort({ highScore: -1 })
      .limit(limit)
      .select('nickname avatar highScore totalScore gamesPlayed')
      .lean();
  }
}
```

## Authentication and Authorization

### Auth Flow

```mermaid
sequenceDiagram
    participant Client as WeChat Client
    participant Server as Backend Server
    participant WeChat as WeChat API
    participant DB as Database
    participant Redis as Redis Cache

    Client->>Server: POST /auth/wechat {code, encryptedData}
    Server->>WeChat: Exchange code for session
    WeChat-->>Server: {openid, session_key}
    
    alt New User
        Server->>WeChat: Decrypt user profile
        Server->>DB: Create user record
        Server->>Redis: Cache user session
    else Existing User
        Server->>DB: Update last active
        Server->>Redis: Update user cache
    end
    
    Server->>Server: Generate JWT token
    Server-->>Client: {token, user, profile}
    
    Note over Client,Redis: User authenticated and ready for game
```

### Middleware/Guards

```typescript
// JWT authentication middleware
import jwt from 'jsonwebtoken';
import { Request, Response, NextFunction } from 'express';
import { UserRepository } from '../repositories/UserRepository';
import { RedisService } from '../services/RedisService';

export interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    wechatId: string;
    nickname: string;
  };
}

export class AuthMiddleware {
  private userRepository: UserRepository;
  private redisService: RedisService;
  private jwtSecret: string;

  constructor() {
    this.userRepository = new UserRepository();
    this.redisService = new RedisService();
    this.jwtSecret = process.env.JWT_SECRET || 'your-secret-key';
  }

  /**
   * Verify JWT token and attach user to request
   */
  public authenticate = async (
    req: AuthenticatedRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const authHeader = req.headers.authorization;
      
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.status(401).json({ error: 'Missing or invalid authorization header' });
      }

      const token = authHeader.substring(7);
      
      // Verify JWT token
      const decoded = jwt.verify(token, this.jwtSecret) as any;
      
      // Check if token is blacklisted (for logout)
      const isBlacklisted = await this.redisService.get(`blacklist:${token}`);
      if (isBlacklisted) {
        return res.status(401).json({ error: 'Token has been revoked' });
      }

      // Check user cache first
      const cacheKey = `user:${decoded.userId}`;
      let user = await this.redisService.get(cacheKey);
      
      if (!user) {
        // Fetch from database
        const userDoc = await this.userRepository.findById(decoded.userId);
        if (!userDoc) {
          return res.status(401).json({ error: 'User not found' });
        }
        
        user = {
          id: userDoc._id.toString(),
          wechatId: userDoc.wechatId,
          nickname: userDoc.nickname
        };
        
        // Cache for 1 hour
        await this.redisService.setex(cacheKey, 3600, JSON.stringify(user));
      } else {
        user = JSON.parse(user);
      }

      req.user = user;
      next();

    } catch (error) {
      if (error instanceof jwt.JsonWebTokenError) {
        return res.status(401).json({ error: 'Invalid token' });
      }
      
      logger.error('Authentication error', error);
      return res.status(500).json({ error: 'Authentication failed' });
    }
  };

  /**
   * Rate limiting middleware
   */
  public rateLimit = (windowMs: number, maxRequests: number) => {
    return async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
      try {
        const userId = req.user?.id || req.ip;
        const key = `rate_limit:${userId}:${Math.floor(Date.now() / windowMs)}`;
        
        const currentCount = await this.redisService.incr(key);
        
        if (currentCount === 1) {
          await this.redisService.expire(key, Math.ceil(windowMs / 1000));
        }
        
        if (currentCount > maxRequests) {
          return res.status(429).json({ 
            error: 'Too many requests', 
            retryAfter: Math.ceil(windowMs / 1000) 
          });
        }
        
        // Add rate limit headers
        res.set({
          'X-RateLimit-Limit': maxRequests.toString(),
          'X-RateLimit-Remaining': Math.max(0, maxRequests - currentCount).toString(),
          'X-RateLimit-Reset': new Date(Date.now() + windowMs).toISOString()
        });
        
        next();
      } catch (error) {
        logger.error('Rate limiting error', error);
        next(); // Continue on rate limit errors
      }
    };
  };

  /**
   * WeChat signature validation middleware
   */
  public validateWeChatSignature = (req: Request, res: Response, next: NextFunction): void => {
    // WeChat webhook signature validation
    const signature = req.headers['x-wechat-signature'] as string;
    const timestamp = req.headers['x-wechat-timestamp'] as string;
    const nonce = req.headers['x-wechat-nonce'] as string;
    
    if (!signature || !timestamp || !nonce) {
      return res.status(400).json({ error: 'Missing WeChat signature headers' });
    }

    // Validate signature using WeChat algorithm
    const isValid = this.verifyWeChatSignature(signature, timestamp, nonce, req.body);
    
    if (!isValid) {
      return res.status(401).json({ error: 'Invalid WeChat signature' });
    }

    next();
  };

  private verifyWeChatSignature(signature: string, timestamp: string, nonce: string, body: any): boolean {
    // WeChat signature verification implementation
    // This would use the actual WeChat signature algorithm
    return true; // Simplified for example
  }
}

export const authMiddleware = new AuthMiddleware();
```

# Unified Project Structure

The monorepo structure is designed to support rapid development with clear separation between client and server while enabling efficient code sharing and coordinated deployment for the WeChat Doodle Jump clone.

```
wechat-doodle-jump/
├── .github/                        # CI/CD workflows and automation
│   ├── workflows/
│   │   ├── ci.yml                  # Continuous integration
│   │   ├── deploy-staging.yml      # Staging deployment
│   │   ├── deploy-production.yml   # Production deployment
│   │   └── security-scan.yml       # Security and vulnerability scanning
│   ├── ISSUE_TEMPLATE/
│   │   ├── bug_report.md
│   │   └── feature_request.md
│   └── PULL_REQUEST_TEMPLATE.md
│
├── apps/                           # Application packages
│   ├── client/                     # WeChat MiniProgram frontend
│   │   ├── src/
│   │   │   ├── components/         # Reusable UI components
│   │   │   │   ├── common/         # Generic components (Button, Modal, etc.)
│   │   │   │   ├── game/           # Game-specific UI (ScoreDisplay, GameMenu)
│   │   │   │   └── social/         # Social components (Leaderboard, Challenges)
│   │   │   ├── scenes/             # Phaser 3 game scenes
│   │   │   │   ├── BootScene.ts
│   │   │   │   ├── MenuScene.ts
│   │   │   │   ├── GameScene.ts
│   │   │   │   ├── GameOverScene.ts
│   │   │   │   ├── LeaderboardScene.ts
│   │   │   │   ├── ShopScene.ts
│   │   │   │   └── SettingsScene.ts
│   │   │   ├── game-objects/       # Phaser game entities
│   │   │   │   ├── Player.ts
│   │   │   │   ├── Platform.ts
│   │   │   │   ├── Powerup.ts
│   │   │   │   └── Background.ts
│   │   │   ├── managers/           # Game system managers
│   │   │   │   ├── GameStateManager.ts
│   │   │   │   ├── ScoreManager.ts
│   │   │   │   ├── InputManager.ts
│   │   │   │   ├── AudioManager.ts
│   │   │   │   └── AssetManager.ts
│   │   │   ├── services/           # External service integrations
│   │   │   │   ├── api/            # API client services
│   │   │   │   │   ├── apiClient.ts
│   │   │   │   │   ├── gameApi.ts
│   │   │   │   │   ├── socialApi.ts
│   │   │   │   │   └── storeApi.ts
│   │   │   │   ├── wechat/         # WeChat SDK integration
│   │   │   │   │   ├── auth.ts
│   │   │   │   │   ├── share.ts
│   │   │   │   │   ├── payment.ts
│   │   │   │   │   └── social.ts
│   │   │   │   └── storage/        # Local storage management
│   │   │   │       ├── localStorage.ts
│   │   │   │       └── sessionStorage.ts
│   │   │   ├── store/              # Redux state management
│   │   │   │   ├── index.ts
│   │   │   │   ├── slices/
│   │   │   │   │   ├── authSlice.ts
│   │   │   │   │   ├── gameSlice.ts
│   │   │   │   │   ├── socialSlice.ts
│   │   │   │   │   ├── storeSlice.ts
│   │   │   │   │   ├── rewardsSlice.ts
│   │   │   │   │   ├── uiSlice.ts
│   │   │   │   │   └── wechatSlice.ts
│   │   │   │   └── middleware/
│   │   │   │       └── apiMiddleware.ts
│   │   │   ├── assets/             # Game assets and resources
│   │   │   │   ├── images/
│   │   │   │   │   ├── characters/
│   │   │   │   │   ├── platforms/
│   │   │   │   │   ├── backgrounds/
│   │   │   │   │   ├── ui/
│   │   │   │   │   └── achievements/
│   │   │   │   ├── audio/
│   │   │   │   │   ├── sfx/
│   │   │   │   │   └── music/
│   │   │   │   └── fonts/
│   │   │   ├── styles/             # Global styles and themes
│   │   │   │   ├── globals.scss
│   │   │   │   ├── variables.scss
│   │   │   │   ├── mixins.scss
│   │   │   │   └── components.scss
│   │   │   ├── utils/              # Frontend utilities
│   │   │   │   ├── constants.ts
│   │   │   │   ├── validators.ts
│   │   │   │   ├── formatters.ts
│   │   │   │   └── helpers.ts
│   │   │   ├── types/              # Frontend-specific types
│   │   │   │   ├── game.ts
│   │   │   │   ├── ui.ts
│   │   │   │   └── wechat.ts
│   │   │   ├── main.ts             # Application entry point
│   │   │   └── config.ts           # Frontend configuration
│   │   ├── public/                 # Static assets for WeChat
│   │   │   ├── game.json           # WeChat MiniProgram configuration
│   │   │   ├── app.json            # WeChat app configuration
│   │   │   ├── project.config.json # WeChat developer tools config
│   │   │   └── sitemap.json        # WeChat sitemap
│   │   ├── tests/                  # Frontend tests
│   │   │   ├── unit/
│   │   │   │   ├── components/
│   │   │   │   ├── services/
│   │   │   │   └── utils/
│   │   │   ├── integration/
│   │   │   │   ├── api/
│   │   │   │   └── wechat/
│   │   │   └── e2e/
│   │   │       ├── gameplay.spec.ts
│   │   │       ├── social.spec.ts
│   │   │       └── purchase.spec.ts
│   │   ├── webpack.config.js       # Webpack build configuration
│   │   ├── tailwind.config.js      # Tailwind CSS configuration
│   │   ├── tsconfig.json           # TypeScript configuration
│   │   ├── jest.config.js          # Jest testing configuration
│   │   ├── .eslintrc.js            # ESLint configuration
│   │   └── package.json            # Frontend dependencies
│   │
│   └── server/                     # Node.js backend API
│       ├── src/
│       │   ├── controllers/        # Request handlers
│       │   │   ├── auth/
│       │   │   │   ├── AuthController.ts
│       │   │   │   └── WeChatAuthController.ts
│       │   │   ├── game/
│       │   │   │   ├── GameController.ts
│       │   │   │   └── SessionController.ts
│       │   │   ├── social/
│       │   │   │   ├── FriendsController.ts
│       │   │   │   ├── LeaderboardController.ts
│       │   │   │   └── ChallengeController.ts
│       │   │   ├── store/
│       │   │   │   ├── StoreController.ts
│       │   │   │   └── PurchaseController.ts
│       │   │   └── rewards/
│       │   │       └── DailyRewardsController.ts
│       │   ├── services/           # Business logic layer
│       │   │   ├── AuthService.ts
│       │   │   ├── GameService.ts
│       │   │   ├── SocialService.ts
│       │   │   ├── PaymentService.ts
│       │   │   ├── RewardsService.ts
│       │   │   ├── NotificationService.ts
│       │   │   └── AnalyticsService.ts
│       │   ├── repositories/       # Data access layer
│       │   │   ├── BaseRepository.ts
│       │   │   ├── UserRepository.ts
│       │   │   ├── GameSessionRepository.ts
│       │   │   ├── AchievementRepository.ts
│       │   │   ├── FriendshipRepository.ts
│       │   │   ├── ChallengeRepository.ts
│       │   │   └── PurchaseRepository.ts
│       │   ├── models/             # Database models
│       │   │   ├── User.ts
│       │   │   ├── GameSession.ts
│       │   │   ├── Achievement.ts
│       │   │   ├── Friendship.ts
│       │   │   ├── Challenge.ts
│       │   │   └── Purchase.ts
│       │   ├── middleware/         # Express middleware
│       │   │   ├── auth.ts
│       │   │   ├── validation.ts
│       │   │   ├── rateLimit.ts
│       │   │   ├── cors.ts
│       │   │   ├── security.ts
│       │   │   └── error.ts
│       │   ├── routes/             # Route definitions
│       │   │   ├── index.ts
│       │   │   ├── auth.ts
│       │   │   ├── game.ts
│       │   │   ├── social.ts
│       │   │   ├── store.ts
│       │   │   ├── rewards.ts
│       │   │   └── health.ts
│       │   ├── sockets/            # Socket.io real-time features
│       │   │   ├── socketManager.ts
│       │   │   ├── gameEvents.ts
│       │   │   ├── socialEvents.ts
│       │   │   └── notificationEvents.ts
│       │   ├── utils/              # Backend utilities
│       │   │   ├── logger.ts
│       │   │   ├── redis.ts
│       │   │   ├── mongodb.ts
│       │   │   ├── wechatSDK.ts
│       │   │   ├── encryption.ts
│       │   │   ├── validators.ts
│       │   │   └── errors.ts
│       │   ├── config/             # Configuration management
│       │   │   ├── database.ts
│       │   │   ├── redis.ts
│       │   │   ├── wechat.ts
│       │   │   ├── alibaba.ts
│       │   │   └── environment.ts
│       │   ├── types/              # Backend-specific types
│       │   │   ├── auth.ts
│       │   │   ├── api.ts
│       │   │   ├── database.ts
│       │   │   └── wechat.ts
│       │   ├── app.ts              # Express application setup
│       │   └── server.ts           # Server entry point
│       ├── tests/                  # Backend tests
│       │   ├── unit/
│       │   │   ├── controllers/
│       │   │   ├── services/
│       │   │   ├── repositories/
│       │   │   └── utils/
│       │   ├── integration/
│       │   │   ├── api/
│       │   │   ├── database/
│       │   │   └── wechat/
│       │   └── fixtures/
│       │       ├── users.json
│       │       ├── sessions.json
│       │       └── challenges.json
│       ├── scripts/               # Deployment and utility scripts
│       │   ├── migrate.ts
│       │   ├── seed.ts
│       │   ├── cleanup.ts
│       │   └── health-check.ts
│       ├── Dockerfile            # Container configuration
│       ├── tsconfig.json         # TypeScript configuration
│       ├── jest.config.js        # Jest testing configuration
│       ├── .eslintrc.js          # ESLint configuration
│       └── package.json          # Backend dependencies
│
├── packages/                     # Shared packages
│   ├── shared/                   # Shared types and utilities
│   │   ├── src/
│   │   │   ├── types/            # Shared TypeScript interfaces
│   │   │   │   ├── index.ts
│   │   │   │   ├── User.ts
│   │   │   │   ├── GameSession.ts
│   │   │   │   ├── Achievement.ts
│   │   │   │   ├── Challenge.ts
│   │   │   │   ├── Purchase.ts
│   │   │   │   ├── Leaderboard.ts
│   │   │   │   └── WeChat.ts
│   │   │   ├── constants/        # Shared constants
│   │   │   │   ├── index.ts
│   │   │   │   ├── game.ts
│   │   │   │   ├── achievements.ts
│   │   │   │   ├── errors.ts
│   │   │   │   └── wechat.ts
│   │   │   ├── utils/            # Shared utilities
│   │   │   │   ├── index.ts
│   │   │   │   ├── validation.ts
│   │   │   │   ├── formatting.ts
│   │   │   │   ├── encryption.ts
│   │   │   │   └── date.ts
│   │   │   └── enums/            # Shared enumerations
│   │   │       ├── index.ts
│   │   │       ├── GameStates.ts
│   │   │       ├── AchievementTypes.ts
│   │   │       └── PaymentStatus.ts
│   │   ├── tsconfig.json
│   │   └── package.json
│   │
│   ├── ui/                       # Shared UI components (if needed)
│   │   ├── src/
│   │   │   ├── components/
│   │   │   │   ├── Button/
│   │   │   │   ├── Modal/
│   │   │   │   └── Loading/
│   │   │   └── styles/
│   │   │       └── shared.scss
│   │   ├── tsconfig.json
│   │   └── package.json
│   │
│   └── config/                   # Shared configuration
│       ├── eslint/
│       │   ├── base.js
│       │   ├── frontend.js
│       │   └── backend.js
│       ├── typescript/
│       │   ├── base.json
│       │   ├── frontend.json
│       │   └── backend.json
│       ├── jest/
│       │   ├── base.js
│       │   ├── frontend.js
│       │   └── backend.js
│       └── prettier/
│           └── .prettierrc.js
│
├── infrastructure/               # Infrastructure as Code
│   ├── terraform/
│   │   ├── environments/
│   │   │   ├── development/
│   │   │   ├── staging/
│   │   │   └── production/
│   │   ├── modules/
│   │   │   ├── alibaba-cloud/
│   │   │   ├── mongodb/
│   │   │   ├── redis/
│   │   │   └── monitoring/
│   │   ├── main.tf
│   │   ├── variables.tf
│   │   ├── outputs.tf
│   │   └── versions.tf
│   ├── docker/
│   │   ├── docker-compose.yml
│   │   ├── docker-compose.dev.yml
│   │   └── nginx.conf
│   └── k8s/                      # Kubernetes manifests (future scaling)
│       ├── deployments/
│       ├── services/
│       └── configmaps/
│
├── scripts/                      # Root-level scripts
│   ├── setup.sh                 # Initial project setup
│   ├── dev.sh                   # Start development environment
│   ├── build.sh                 # Build all packages
│   ├── test.sh                  # Run all tests
│   ├── deploy.sh                # Deployment script
│   └── cleanup.sh               # Cleanup script
│
├── docs/                         # Documentation
│   ├── architecture.md          # This document
│   ├── prd.md                    # Product requirements
│   ├── brief.md                  # Project brief
│   ├── market-research.md        # Market analysis
│   ├── api.md                    # API documentation
│   ├── deployment.md             # Deployment guide
│   ├── development.md            # Development guide
│   └── troubleshooting.md        # Common issues and solutions
│
├── .env.example                  # Environment variables template
├── .gitignore                    # Git ignore rules
├── .prettierrc                   # Code formatting rules
├── package.json                  # Root package.json with workspaces
├── lerna.json                    # Lerna configuration (if using Lerna)
├── nx.json                       # Nx configuration (if using Nx)
├── tsconfig.base.json            # Base TypeScript configuration
├── LICENSE                       # License file
└── README.md                     # Project overview and setup
```

## Key Structure Benefits

### Monorepo Advantages

**Code Sharing**: Shared types, constants, and utilities in `packages/shared` eliminate duplication and ensure consistency between frontend and backend.

**Coordinated Development**: Changes to shared types automatically propagate to both applications, preventing API contract mismatches.

**Unified Tooling**: Single ESLint, Prettier, and TypeScript configuration across the entire codebase maintains consistency.

**Atomic Commits**: Frontend and backend changes can be committed together, ensuring features are complete and deployable.

**Simplified CI/CD**: Single pipeline can build, test, and deploy both applications with proper dependency management.

### Workspace Configuration

The root `package.json` uses npm workspaces for efficient dependency management:

```json
{
  "name": "wechat-doodle-jump",
  "private": true,
  "workspaces": [
    "apps/*",
    "packages/*"
  ],
  "scripts": {
    "dev": "concurrently \"npm run dev:client\" \"npm run dev:server\"",
    "dev:client": "npm run dev --workspace=apps/client",
    "dev:server": "npm run dev --workspace=apps/server",
    "build": "npm run build --workspaces",
    "test": "npm run test --workspaces",
    "lint": "npm run lint --workspaces",
    "type-check": "npm run type-check --workspaces"
  },
  "devDependencies": {
    "concurrently": "^7.6.0",
    "lerna": "^6.4.1",
    "nx": "^15.5.2"
  }
}
```

### Development Workflow Optimization

**Hot Reloading**: Client and server can run simultaneously with hot reloading for rapid development iterations.

**Type Safety**: Shared types ensure compile-time verification of API contracts between client and server.

**Asset Management**: Centralized asset management with optimization for WeChat's 20MB limit.

**Environment Management**: Consistent environment configuration across development, staging, and production.

### WeChat-Specific Optimizations

**WeChat Configuration**: Proper WeChat MiniProgram configuration files in the client's public directory.

**Asset Optimization**: Webpack configuration optimized for WeChat's size and performance constraints.

**Compliance**: Infrastructure setup for China deployment requirements and regulatory compliance.

**Testing Strategy**: Comprehensive testing structure supporting unit, integration, and E2E tests for WeChat platform.

This unified structure directly supports the aggressive 1-week MVP timeline through efficient code sharing, streamlined development workflows, and optimized build processes while maintaining clear separation of concerns and scalability for post-MVP growth.