#!/bin/bash

# WeChat Doodle Jump - Development Setup Script
# This script sets up the development environment for the project

set -e

echo "🎮 WeChat Doodle Jump - Development Setup"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Node.js is installed
check_nodejs() {
    print_status "Checking Node.js installation..."
    
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 18+ from https://nodejs.org/"
        exit 1
    fi
    
    NODE_VERSION=$(node --version | cut -d'v' -f2)
    MAJOR_VERSION=$(echo $NODE_VERSION | cut -d'.' -f1)
    
    if [ "$MAJOR_VERSION" -lt 18 ]; then
        print_error "Node.js version $NODE_VERSION is too old. Please install Node.js 18+"
        exit 1
    fi
    
    print_success "Node.js $NODE_VERSION is installed"
}

# Check if npm is installed
check_npm() {
    print_status "Checking npm installation..."
    
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed. Please install npm 9+"
        exit 1
    fi
    
    NPM_VERSION=$(npm --version)
    MAJOR_VERSION=$(echo $NPM_VERSION | cut -d'.' -f1)
    
    if [ "$MAJOR_VERSION" -lt 9 ]; then
        print_warning "npm version $NPM_VERSION is old. Please consider upgrading to npm 9+"
    fi
    
    print_success "npm $NPM_VERSION is installed"
}

# Check if MongoDB is running
check_mongodb() {
    print_status "Checking MongoDB connection..."
    
    if ! command -v mongosh &> /dev/null && ! command -v mongo &> /dev/null; then
        print_warning "MongoDB client not found. Please install MongoDB"
        return 1
    fi
    
    # Try to connect to MongoDB
    if mongosh --eval "db.runCommand('ping')" &> /dev/null || mongo --eval "db.runCommand('ping')" &> /dev/null; then
        print_success "MongoDB is running and accessible"
    else
        print_warning "MongoDB is not running or not accessible on default port"
        print_status "Please start MongoDB with: mongod --dbpath ./data/db"
    fi
}

# Check if Redis is running
check_redis() {
    print_status "Checking Redis connection..."
    
    if ! command -v redis-cli &> /dev/null; then
        print_warning "Redis client not found. Please install Redis"
        return 1
    fi
    
    # Try to ping Redis
    if redis-cli ping &> /dev/null; then
        print_success "Redis is running and accessible"
    else
        print_warning "Redis is not running or not accessible on default port"
        print_status "Please start Redis with: redis-server"
    fi
}

# Install dependencies
install_dependencies() {
    print_status "Installing project dependencies..."
    
    if [ -f "package-lock.json" ]; then
        npm ci
    else
        npm install
    fi
    
    print_success "Dependencies installed successfully"
}

# Create environment file
setup_environment() {
    print_status "Setting up environment configuration..."
    
    if [ ! -f ".env" ]; then
        if [ -f ".env.example" ]; then
            cp .env.example .env
            print_success "Environment file created from template"
            print_warning "Please edit .env file with your configuration values"
        else
            print_error ".env.example file not found"
            exit 1
        fi
    else
        print_success "Environment file already exists"
    fi
}

# Create data directories
create_directories() {
    print_status "Creating necessary directories..."
    
    mkdir -p data/db
    mkdir -p logs
    mkdir -p uploads
    mkdir -p certs
    
    print_success "Directories created successfully"
}

# Setup Git hooks
setup_git_hooks() {
    print_status "Setting up Git hooks..."
    
    if [ -d ".git" ]; then
        npm run prepare
        print_success "Git hooks configured"
    else
        print_warning "Not a Git repository. Skipping Git hooks setup"
    fi
}

# Run initial build
run_initial_build() {
    print_status "Running initial build..."
    
    npm run build
    
    print_success "Initial build completed"
}

# Run tests
run_tests() {
    print_status "Running tests to verify setup..."
    
    npm test
    
    print_success "All tests passed"
}

# Main setup function
main() {
    echo
    print_status "Starting development environment setup..."
    echo
    
    # Pre-flight checks
    check_nodejs
    check_npm
    
    # Database checks (warnings only)
    check_mongodb || true
    check_redis || true
    
    echo
    print_status "Installing and configuring project..."
    echo
    
    # Setup project
    install_dependencies
    setup_environment
    create_directories
    setup_git_hooks
    
    echo
    print_status "Building and testing project..."
    echo
    
    # Build and test
    run_initial_build
    run_tests
    
    echo
    print_success "🎉 Development environment setup completed!"
    echo
    print_status "Next steps:"
    echo "  1. Edit .env file with your configuration"
    echo "  2. Start MongoDB: mongod --dbpath ./data/db"
    echo "  3. Start Redis: redis-server" 
    echo "  4. Start development servers: npm run dev"
    echo "  5. Open WeChat Developer Tools and import apps/client/"
    echo
    print_status "Happy coding! 🚀"
}

# Run main function
main "$@"