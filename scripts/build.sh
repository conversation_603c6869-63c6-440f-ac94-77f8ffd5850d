#!/bin/bash

# WeChat Doodle Jump - Production Build Script
# This script builds the project for production deployment

set -e

echo "🔨 WeChat Doodle Jump - Production Build"
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
BUILD_ENV=${NODE_ENV:-production}
BUILD_DIR="dist"
CLIENT_DIR="apps/client"
API_DIR="apps/api"

print_status "Building for environment: $BUILD_ENV"

# Clean previous builds
clean_builds() {
    print_status "Cleaning previous builds..."
    
    rm -rf $CLIENT_DIR/dist
    rm -rf $API_DIR/dist
    rm -rf packages/shared/dist
    
    print_success "Previous builds cleaned"
}

# Build shared packages first
build_shared() {
    print_status "Building shared packages..."
    
    npm run build --workspace=packages/shared
    
    print_success "Shared packages built"
}

# Build client application
build_client() {
    print_status "Building client application..."
    
    # Set production environment
    export NODE_ENV=production
    
    # Build the client
    npm run build --workspace=apps/client
    
    # Verify build output
    if [ ! -d "$CLIENT_DIR/dist" ]; then
        print_error "Client build failed - dist directory not found"
        exit 1
    fi
    
    # Check build size (WeChat 20MB limit)
    CLIENT_SIZE=$(du -sm $CLIENT_DIR/dist | cut -f1)
    print_status "Client build size: ${CLIENT_SIZE}MB"
    
    if [ "$CLIENT_SIZE" -gt 18 ]; then
        print_warning "Client build size (${CLIENT_SIZE}MB) is approaching WeChat 20MB limit"
    fi
    
    print_success "Client application built successfully"
}

# Build API server
build_api() {
    print_status "Building API server..."
    
    # Set production environment
    export NODE_ENV=production
    
    # Build the API
    npm run build --workspace=apps/api
    
    # Verify build output
    if [ ! -d "$API_DIR/dist" ]; then
        print_error "API build failed - dist directory not found"
        exit 1
    fi
    
    print_success "API server built successfully"
}

# Run type checking
run_typecheck() {
    print_status "Running TypeScript type checking..."
    
    npm run typecheck
    
    if [ $? -eq 0 ]; then
        print_success "Type checking passed"
    else
        print_error "Type checking failed"
        exit 1
    fi
}

# Run linting
run_linting() {
    print_status "Running code linting..."
    
    npm run lint
    
    if [ $? -eq 0 ]; then
        print_success "Linting passed"
    else
        print_error "Linting failed"
        exit 1
    fi
}

# Run tests
run_tests() {
    print_status "Running tests..."
    
    # Set test environment
    export NODE_ENV=test
    
    npm test
    
    if [ $? -eq 0 ]; then
        print_success "All tests passed"
    else
        print_error "Tests failed"
        exit 1
    fi
}

# Generate build info
generate_build_info() {
    print_status "Generating build information..."
    
    BUILD_TIME=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    GIT_COMMIT=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
    GIT_BRANCH=$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo "unknown")
    
    # Create build info file
    cat > build-info.json << EOF
{
  "buildTime": "$BUILD_TIME",
  "gitCommit": "$GIT_COMMIT",
  "gitBranch": "$GIT_BRANCH",
  "nodeVersion": "$(node --version)",
  "npmVersion": "$(npm --version)",
  "environment": "$BUILD_ENV"
}
EOF
    
    # Copy to build directories
    cp build-info.json $CLIENT_DIR/dist/ 2>/dev/null || true
    cp build-info.json $API_DIR/dist/ 2>/dev/null || true
    
    print_success "Build information generated"
}

# Create deployment package
create_deployment_package() {
    print_status "Creating deployment package..."
    
    # Create deployment directory
    mkdir -p deployment
    
    # Package client
    cd $CLIENT_DIR/dist
    tar -czf ../../deployment/client-${BUILD_ENV}-${GIT_COMMIT}.tar.gz .
    cd ../../
    
    # Package API
    cd $API_DIR/dist
    tar -czf ../../deployment/api-${BUILD_ENV}-${GIT_COMMIT}.tar.gz .
    cd ../../
    
    # Package shared
    if [ -d "packages/shared/dist" ]; then
        cd packages/shared/dist
        tar -czf ../../../deployment/shared-${BUILD_ENV}-${GIT_COMMIT}.tar.gz .
        cd ../../../
    fi
    
    print_success "Deployment packages created in deployment/"
}

# Validate WeChat MiniProgram requirements
validate_wechat_requirements() {
    print_status "Validating WeChat MiniProgram requirements..."
    
    # Check required files
    REQUIRED_FILES=("$CLIENT_DIR/dist/game.json" "$CLIENT_DIR/dist/game.js")
    
    for file in "${REQUIRED_FILES[@]}"; do
        if [ ! -f "$file" ]; then
            print_error "Required file missing: $file"
            exit 1
        fi
    done
    
    # Check game.json format
    if ! python3 -m json.tool $CLIENT_DIR/dist/game.json > /dev/null 2>&1; then
        print_error "Invalid game.json format"
        exit 1
    fi
    
    print_success "WeChat MiniProgram requirements validated"
}

# Main build function
main() {
    echo
    print_status "Starting production build process..."
    echo
    
    # Pre-build validation
    print_status "Running pre-build validation..."
    run_typecheck
    run_linting
    run_tests
    
    echo
    print_status "Building applications..."
    echo
    
    # Build process
    clean_builds
    build_shared
    build_client
    build_api
    
    echo
    print_status "Post-build validation and packaging..."
    echo
    
    # Post-build steps
    validate_wechat_requirements
    generate_build_info
    create_deployment_package
    
    echo
    print_success "🎉 Production build completed successfully!"
    echo
    print_status "Build artifacts:"
    echo "  - Client: $CLIENT_DIR/dist/"
    echo "  - API: $API_DIR/dist/"
    echo "  - Packages: deployment/"
    echo
    print_status "Next steps:"
    echo "  1. Upload client build to WeChat Developer Console"
    echo "  2. Deploy API to Alibaba Cloud"
    echo "  3. Update CDN with new assets"
    echo "  4. Run smoke tests on staging environment"
    echo
    print_status "Build completed! 🚀"
}

# Handle command line arguments
case "${1:-build}" in
    "clean")
        clean_builds
        ;;
    "client")
        build_shared
        build_client
        validate_wechat_requirements
        ;;
    "api")
        build_shared
        build_api
        ;;
    "test")
        run_tests
        ;;
    "build"|*)
        main
        ;;
esac