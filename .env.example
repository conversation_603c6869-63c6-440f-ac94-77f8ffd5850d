# Environment Configuration Template
# Copy this file to .env and fill in your values

# === NODE ENVIRONMENT ===
NODE_ENV=development

# === DATABASE ===
MONGODB_URI=mongodb://localhost:27017/wechat_doodle_jump
MONGODB_TEST_URI=mongodb://localhost:27017/wechat_doodle_jump_test

# === REDIS CACHE ===
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=

# === JWT AUTHENTICATION ===
JWT_SECRET=your-super-secure-jwt-secret-key-change-this-in-production
JWT_EXPIRES_IN=7d

# === WECHAT API ===
WECHAT_APP_ID=your-wechat-miniprogram-app-id
WECHAT_APP_SECRET=your-wechat-miniprogram-app-secret

# === WECHAT PAY ===
WECHAT_PAY_MERCHANT_ID=your-wechat-pay-merchant-id
WECHAT_PAY_KEY=your-wechat-pay-api-key
WECHAT_PAY_CERT_PATH=./certs/wechat-pay-cert.pem
WECHAT_PAY_KEY_PATH=./certs/wechat-pay-key.pem

# === SERVER CONFIGURATION ===
PORT=3001
API_BASE_URL=http://localhost:3001
CLIENT_URL=http://localhost:3000

# === CORS ORIGINS ===
CORS_ORIGINS=http://localhost:3000,https://servicewechat.com

# === RATE LIMITING ===
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# === ALIBABA CLOUD (Production) ===
ALIBABA_ACCESS_KEY_ID=your-alibaba-access-key
ALIBABA_ACCESS_KEY_SECRET=your-alibaba-secret-key
ALIBABA_REGION=cn-hangzhou

# === CDN ===
CDN_URL=https://your-cdn-domain.com

# === MONITORING ===
SENTRY_DSN=your-sentry-dsn-url

# === LOGGING ===
LOG_LEVEL=info
LOG_FILE_PATH=./logs/app.log

# === GAME CONFIGURATION ===
MAX_LEADERBOARD_SIZE=100
DAILY_REWARD_RESET_HOUR=0
ACHIEVEMENT_CACHE_TTL=3600

# === DEVELOPMENT ONLY ===
MOCK_WECHAT_API=true
DEBUG_MODE=true